#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 生产级修复验证测试
验证OKX历史时间戳问题的真正修复效果
必须模拟真实WebSocket数据流场景
"""

import asyncio
import time
import json
import sys
import os
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class ProductionFixVerification:
    """生产级修复验证"""
    
    def __init__(self):
        self.test_results = {
            "timestamp": int(time.time()),
            "tests_passed": 0,
            "tests_total": 0,
            "critical_issues": [],
            "fix_effectiveness": {}
        }
    
    async def test_okx_historical_timestamp_fix(self):
        """测试OKX历史时间戳修复效果"""
        print("🔍 测试OKX历史时间戳修复效果...")
        self.test_results["tests_total"] += 1
        
        try:
            from websocket.unified_timestamp_processor import get_synced_timestamp
            
            # 模拟OKX WebSocket返回的历史数据（类似实际日志中的情况）
            historical_okx_data = {
                "arg": {"channel": "books", "instId": "BTC-USDT"},
                "data": [{
                    "asks": [["50000", "1.0"]],
                    "bids": [["49900", "1.0"]],
                    "ts": "1754035947700",  # 这是实际日志中的历史时间戳（6379秒前）
                    "checksum": 12345
                }]
            }
            
            # 测试1：验证get_synced_timestamp对OKX历史数据的处理
            print("  📊 测试1: OKX历史时间戳处理...")
            result_timestamp = get_synced_timestamp("okx", historical_okx_data)
            current_time = int(time.time() * 1000)
            time_diff = abs(result_timestamp - current_time)
            
            print(f"    - 历史ts: {historical_okx_data['data'][0]['ts']}")
            print(f"    - 返回时间戳: {result_timestamp}")
            print(f"    - 当前时间: {current_time}")
            print(f"    - 时间差: {time_diff:.1f}ms")
            
            # 验证修复效果：时间差应该在合理范围内（<2秒）
            if time_diff < 2000:
                print("    ✅ OKX历史时间戳修复成功：时间差在合理范围内")
                self.test_results["tests_passed"] += 1
                self.test_results["fix_effectiveness"]["okx_historical_timestamp"] = "FIXED"
            else:
                print(f"    ❌ OKX历史时间戳修复失败：时间差{time_diff:.1f}ms > 2000ms")
                self.test_results["critical_issues"].append(f"OKX历史时间戳问题未修复：时间差{time_diff:.1f}ms")
                self.test_results["fix_effectiveness"]["okx_historical_timestamp"] = "NOT_FIXED"
            
            # 测试2：验证OKX数据不会被时间戳过期机制丢弃
            print("  📊 测试2: OKX数据不应被丢弃...")
            from websocket.unified_timestamp_processor import get_timestamp_processor
            processor = get_timestamp_processor("okx")
            
            # 直接测试时间戳获取逻辑
            test_timestamp = processor.get_synced_timestamp(historical_okx_data)
            time_diff = abs(test_timestamp - current_time)
            
            if time_diff < 2000:
                print("    ✅ OKX数据不会被过期机制丢弃")
                self.test_results["tests_passed"] += 1
            else:
                print(f"    ❌ OKX数据仍会被过期机制丢弃：时间差{time_diff:.1f}ms")
                self.test_results["critical_issues"].append("OKX数据仍会被过期机制丢弃")
            
            self.test_results["tests_total"] += 1
            
        except Exception as e:
            print(f"    ❌ 测试异常: {e}")
            self.test_results["critical_issues"].append(f"OKX时间戳测试异常: {e}")
    
    async def test_cross_exchange_sync_with_800ms_limit(self):
        """测试跨交易所同步在800ms极限下的表现"""
        print("🔍 测试跨交易所800ms极限同步...")
        self.test_results["tests_total"] += 1
        
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            processor = get_timestamp_processor("gate")
            current_time = int(time.time() * 1000)
            
            # 测试场景1：正常800ms内的时间差
            timestamp1 = current_time
            timestamp2 = current_time + 500  # 500ms差异
            
            is_synced, time_diff = processor.validate_cross_exchange_sync(
                timestamp1, timestamp2, "gate", "okx", max_diff_ms=800
            )
            
            print(f"  📊 场景1 (500ms差异): 同步={is_synced}, 时间差={time_diff:.1f}ms")
            
            if is_synced and time_diff <= 800:
                print("    ✅ 正常情况下800ms限制工作正常")
                self.test_results["tests_passed"] += 1
            else:
                print("    ❌ 800ms限制在正常情况下异常")
                self.test_results["critical_issues"].append("800ms限制在正常情况下异常")
            
            # 测试场景2：超过800ms的时间差
            timestamp3 = current_time + 1000  # 1000ms差异，超过800ms限制
            
            is_synced2, time_diff2 = processor.validate_cross_exchange_sync(
                timestamp1, timestamp3, "gate", "okx", max_diff_ms=800
            )
            
            print(f"  📊 场景2 (1000ms差异): 同步={is_synced2}, 时间差={time_diff2:.1f}ms")
            
            if not is_synced2 and time_diff2 > 800:
                print("    ✅ 超过800ms时正确识别为不同步")
                self.test_results["tests_passed"] += 1
            else:
                print("    ❌ 超过800ms时同步判断异常")
                self.test_results["critical_issues"].append("超过800ms时同步判断异常")
            
            self.test_results["tests_total"] += 1
            
        except Exception as e:
            print(f"    ❌ 测试异常: {e}")
            self.test_results["critical_issues"].append(f"跨交易所同步测试异常: {e}")
    
    async def test_websocket_data_flow_simulation(self):
        """模拟WebSocket数据流，测试实际场景"""
        print("🔍 模拟WebSocket数据流测试...")
        self.test_results["tests_total"] += 1
        
        try:
            # 模拟3个交易所的真实数据流
            current_time = int(time.time() * 1000)
            
            # Gate数据（正常实时数据）
            gate_data = {
                "method": "books.update",
                "params": ["BTC-USDT", {
                    "asks": [["50000", "1.0"]],
                    "bids": [["49900", "1.0"]],
                    "time_ms": current_time - 100  # 100ms前的数据
                }]
            }
            
            # OKX数据（历史数据，类似实际问题）
            okx_data = {
                "arg": {"channel": "books", "instId": "BTC-USDT"},
                "data": [{
                    "asks": [["50100", "1.0"]],
                    "bids": [["50000", "1.0"]],
                    "ts": str(current_time - 6379000)  # 6379秒前的历史数据
                }]
            }
            
            # Bybit数据（正常数据）
            bybit_data = {
                "topic": "orderbook.1.BTCUSDT",
                "data": {
                    "a": [["50050", "1.0"]],
                    "b": [["49950", "1.0"]],
                    "ts": current_time - 200  # 200ms前的数据
                }
            }
            
            # 测试时间戳提取和处理
            from websocket.unified_timestamp_processor import get_synced_timestamp
            
            gate_ts = get_synced_timestamp("gate", gate_data)
            okx_ts = get_synced_timestamp("okx", okx_data)
            bybit_ts = get_synced_timestamp("bybit", bybit_data)
            
            gate_diff = abs(gate_ts - current_time)
            okx_diff = abs(okx_ts - current_time)
            bybit_diff = abs(bybit_ts - current_time)
            
            print(f"  📊 Gate时间戳差异: {gate_diff:.1f}ms")
            print(f"  📊 OKX时间戳差异: {okx_diff:.1f}ms")
            print(f"  📊 Bybit时间戳差异: {bybit_diff:.1f}ms")
            
            # 验证修复效果
            all_within_limit = all(diff < 2000 for diff in [gate_diff, okx_diff, bybit_diff])
            
            if all_within_limit:
                print("    ✅ 所有交易所时间戳都在合理范围内")
                self.test_results["tests_passed"] += 1
                self.test_results["fix_effectiveness"]["websocket_data_flow"] = "FIXED"
            else:
                print("    ❌ 存在交易所时间戳超出合理范围")
                self.test_results["critical_issues"].append("存在交易所时间戳超出合理范围")
                self.test_results["fix_effectiveness"]["websocket_data_flow"] = "NOT_FIXED"
            
        except Exception as e:
            print(f"    ❌ 测试异常: {e}")
            self.test_results["critical_issues"].append(f"WebSocket数据流测试异常: {e}")
    
    async def run_all_tests(self):
        """运行所有生产级测试"""
        print("🏛️ 启动生产级修复验证测试")
        print("=" * 80)
        
        await self.test_okx_historical_timestamp_fix()
        await self.test_cross_exchange_sync_with_800ms_limit()
        await self.test_websocket_data_flow_simulation()
        
        print("=" * 80)
        print("📊 生产级验证报告")
        print("=" * 80)
        
        success_rate = (self.test_results["tests_passed"] / self.test_results["tests_total"]) * 100
        print(f"通过测试: {self.test_results['tests_passed']}/{self.test_results['tests_total']} ({success_rate:.1f}%)")
        
        if self.test_results["critical_issues"]:
            print(f"发现关键问题: {len(self.test_results['critical_issues'])}")
            for issue in self.test_results["critical_issues"]:
                print(f"  ❌ {issue}")
        else:
            print("发现关键问题: 0")
        
        print("\n修复效果:")
        for fix, status in self.test_results["fix_effectiveness"].items():
            status_icon = "✅" if status == "FIXED" else "❌"
            print(f"  {status_icon} {fix}: {status}")
        
        print("=" * 80)
        
        if success_rate == 100 and len(self.test_results["critical_issues"]) == 0:
            print("🏆 生产级验证结论: 修复完美，可以部署")
            return True
        else:
            print("⚠️ 生产级验证结论: 修复不完整，需要继续修复")
            return False

async def main():
    """主函数"""
    verification = ProductionFixVerification()
    success = await verification.run_all_tests()
    
    # 保存详细结果
    result_file = "diagnostic_results/production_fix_verification.json"
    os.makedirs(os.path.dirname(result_file), exist_ok=True)
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(verification.test_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 详细结果已保存到: {result_file}")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())