#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证系统是否能正常处理配置中的支持交易对
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_supported_symbols():
    """测试支持的交易对处理"""
    print("🔍 测试系统对支持交易对的处理...")
    
    try:
        from core.opportunity_scanner import OpportunityScanner
        scanner = OpportunityScanner()
        
        print(f"📊 支持的交易对: {scanner.supported_symbols}")
        
        # 使用第一个支持的交易对进行测试
        if scanner.supported_symbols:
            test_symbol = scanner.supported_symbols[0]
            print(f"🎯 测试交易对: {test_symbol}")
            
            # 模拟市场数据
            test_data = {
                'exchange': 'gate',
                'symbol': test_symbol,
                'market_type': 'spot',
                'price': 1.5,
                'asks': [[1.51, 1000], [1.52, 2000]],
                'bids': [[1.50, 1000], [1.49, 2000]],
                'timestamp': int(time.time() * 1000)
            }
            
            print(f"📡 发送测试数据: {test_data}")
            
            # 调用_on_market_data方法
            await scanner._on_market_data(test_data)
            
            # 检查数据是否被存储
            data_key = f"gate_spot_{test_symbol}"
            print(f"🔍 检查数据键: {data_key}")
            print(f"📊 当前market_data键: {list(scanner.market_data.keys())}")
            
            if data_key in scanner.market_data:
                market_data = scanner.market_data[data_key]
                print(f"✅ 数据存储成功!")
                print(f"   - 交易所: {market_data.exchange}")
                print(f"   - 交易对: {market_data.symbol}")
                print(f"   - 价格: {market_data.price}")
                print(f"   - 时间戳: {market_data.timestamp}")
                
                # 测试价差扫描
                print("\n🔍 测试价差扫描...")
                opportunities = scanner._scan_symbol_opportunities(test_symbol)
                print(f"📈 发现套利机会数量: {len(opportunities)}")
                
                if opportunities:
                    for opp in opportunities:
                        print(f"   📊 机会: {opp}")
                else:
                    print("   ℹ️ 当前无套利机会（需要同时有现货和期货数据）")
                
                return True
            else:
                print(f"❌ 数据未存储 - 可能仍有过滤问题")
                return False
        else:
            print("❌ 没有支持的交易对配置")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始验证支持交易对处理...")
    success = await test_supported_symbols()
    
    if success:
        print(f"\n🎉 系统正常处理支持的交易对！")
        print(f"💡 原问题确实是因为测试用了不支持的ADA-USDT交易对")
        return True
    else:
        print(f"\n💥 系统仍有问题需要修复")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)