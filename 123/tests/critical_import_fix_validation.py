#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏛️ 机构级别导入修复验证测试
专门验证get_synced_timestamp导入修复的完整性和正确性
"""

import asyncio
import sys
import traceback
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class CriticalImportFixValidator:
    """导入修复验证器 - 机构级别测试"""
    
    def __init__(self):
        self.test_results = {
            'phase1_basic': {'tests': [], 'passed': 0, 'total': 0},
            'phase2_system': {'tests': [], 'passed': 0, 'total': 0},
            'phase3_production': {'tests': [], 'passed': 0, 'total': 0}
        }
        
    def log_test_result(self, phase: str, test_name: str, passed: bool, details: str = ""):
        """记录测试结果"""
        result = {
            'name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': time.time()
        }
        
        self.test_results[phase]['tests'].append(result)
        self.test_results[phase]['total'] += 1
        if passed:
            self.test_results[phase]['passed'] += 1
            
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} [{phase}] {test_name}")
        if details:
            print(f"    详情: {details}")
            
    async def phase1_basic_core_tests(self):
        """① 基础核心测试：模块单元功能验证"""
        print("\n🔥 Phase 1: 基础核心测试 - 导入功能验证")
        print("=" * 80)
        
        # 测试1：统一时间戳处理器导入测试
        try:
            from websocket.unified_timestamp_processor import get_synced_timestamp
            self.log_test_result('phase1_basic', '统一时间戳处理器导入', True, 
                               "get_synced_timestamp函数导入成功")
        except Exception as e:
            self.log_test_result('phase1_basic', '统一时间戳处理器导入', False, 
                               f"导入失败: {str(e)}")
        
        # 测试2：函数调用基础测试
        try:
            from websocket.unified_timestamp_processor import get_synced_timestamp
            timestamp = get_synced_timestamp("system", None)
            if isinstance(timestamp, int) and timestamp > 0:
                self.log_test_result('phase1_basic', '时间戳函数调用', True, 
                                   f"返回有效时间戳: {timestamp}")
            else:
                self.log_test_result('phase1_basic', '时间戳函数调用', False, 
                                   f"返回无效时间戳: {timestamp}")
        except Exception as e:
            self.log_test_result('phase1_basic', '时间戳函数调用', False, 
                               f"函数调用失败: {str(e)}")
        
        # 测试3：OpportunityScanner导入验证
        try:
            from core.opportunity_scanner import OpportunityScanner
            scanner = OpportunityScanner()
            if hasattr(scanner, '_on_market_data'):
                self.log_test_result('phase1_basic', 'OpportunityScanner类导入', True, 
                                   "_on_market_data方法存在")
            else:
                self.log_test_result('phase1_basic', 'OpportunityScanner类导入', False, 
                                   "_on_market_data方法不存在")
        except Exception as e:
            self.log_test_result('phase1_basic', 'OpportunityScanner类导入', False, 
                               f"类导入失败: {str(e)}")
        
        # 测试4：错误场景下的导入稳定性
        try:
            # 模拟在方法内部导入的情况
            def simulate_method_import():
                from websocket.unified_timestamp_processor import get_synced_timestamp
                return get_synced_timestamp("test", {"timestamp": int(time.time() * 1000)})
            
            result = simulate_method_import()
            if isinstance(result, int):
                self.log_test_result('phase1_basic', '方法内部导入稳定性', True, 
                                   f"方法内导入成功，返回: {result}")
            else:
                self.log_test_result('phase1_basic', '方法内部导入稳定性', False, 
                                   f"返回类型错误: {type(result)}")
        except Exception as e:
            self.log_test_result('phase1_basic', '方法内部导入稳定性', False, 
                               f"方法内导入失败: {str(e)}")
    
    async def phase2_system_cascade_tests(self):
        """② 复杂系统级联测试：模块交互和状态联动"""
        print("\n🔥 Phase 2: 复杂系统级联测试 - 模块交互验证")
        print("=" * 80)
        
        # 测试1：OpportunityScanner数据处理流程
        try:
            from core.opportunity_scanner import OpportunityScanner
            scanner = OpportunityScanner()
            
            # 模拟市场数据
            test_data = {
                'exchange': 'gate',
                'symbol': 'ADA-USDT',
                'market_type': 'spot',
                'price': 0.5,
                'asks': [[0.51, 1000], [0.52, 2000]],
                'bids': [[0.50, 1000], [0.49, 2000]],
                'timestamp': int(time.time() * 1000)
            }
            
            # 调用_on_market_data方法
            await scanner._on_market_data(test_data)
            
            # 检查market_data是否被正确填充
            data_key = "gate_spot_ADA-USDT"
            if data_key in scanner.market_data:
                market_data = scanner.market_data[data_key]
                if hasattr(market_data, 'timestamp') and market_data.timestamp > 0:
                    self.log_test_result('phase2_system', 'OpportunityScanner数据处理', True, 
                                       f"数据成功处理，时间戳: {market_data.timestamp}")
                else:
                    self.log_test_result('phase2_system', 'OpportunityScanner数据处理', False, 
                                       "时间戳字段无效")
            else:
                self.log_test_result('phase2_system', 'OpportunityScanner数据处理', False, 
                                   f"数据未存储到market_data，keys: {list(scanner.market_data.keys())}")
                
        except Exception as e:
            self.log_test_result('phase2_system', 'OpportunityScanner数据处理', False, 
                               f"数据处理异常: {str(e)}")
            print(f"详细错误: {traceback.format_exc()}")
        
        # 测试2：多交易所数据处理一致性
        try:
            from core.opportunity_scanner import OpportunityScanner
            scanner = OpportunityScanner()
            
            exchanges = ['gate', 'bybit', 'okx']
            success_count = 0
            
            for exchange in exchanges:
                test_data = {
                    'exchange': exchange,
                    'symbol': 'ADA-USDT',
                    'market_type': 'spot',
                    'price': 0.5,
                    'asks': [[0.51, 1000]],
                    'bids': [[0.50, 1000]],
                    'timestamp': int(time.time() * 1000)
                }
                
                try:
                    await scanner._on_market_data(test_data)
                    data_key = f"{exchange}_spot_ADA-USDT"
                    if data_key in scanner.market_data:
                        success_count += 1
                except Exception as e:
                    print(f"    {exchange}处理失败: {str(e)}")
            
            if success_count == len(exchanges):
                self.log_test_result('phase2_system', '多交易所一致性', True, 
                                   f"所有{len(exchanges)}个交易所数据处理成功")
            else:
                self.log_test_result('phase2_system', '多交易所一致性', False, 
                                   f"只有{success_count}/{len(exchanges)}个交易所成功")
                
        except Exception as e:
            self.log_test_result('phase2_system', '多交易所一致性', False, 
                               f"多交易所测试异常: {str(e)}")
        
        # 测试3：错误恢复机制验证
        try:
            from core.opportunity_scanner import OpportunityScanner
            scanner = OpportunityScanner()
            
            # 模拟错误数据
            invalid_data = {
                'exchange': 'gate',
                'symbol': 'INVALID-SYMBOL',
                'market_type': 'spot',
                'price': -1,  # 无效价格
            }
            
            # 调用错误恢复机制
            await scanner._attempt_error_recovery(invalid_data, "测试错误恢复")
            
            self.log_test_result('phase2_system', '错误恢复机制', True, 
                               "错误恢复机制正常工作")
                
        except Exception as e:
            self.log_test_result('phase2_system', '错误恢复机制', False, 
                               f"错误恢复失败: {str(e)}")
    
    async def phase3_production_tests(self):
        """③ 生产模拟测试：真实场景回放和压力测试"""
        print("\n🔥 Phase 3: 生产模拟测试 - 真实场景验证")
        print("=" * 80)
        
        # 测试1：高频数据处理压力测试
        try:
            from core.opportunity_scanner import OpportunityScanner
            scanner = OpportunityScanner()
            
            start_time = time.time()
            success_count = 0
            error_count = 0
            
            # 模拟100次高频数据更新
            for i in range(100):
                test_data = {
                    'exchange': 'gate',
                    'symbol': 'ADA-USDT',
                    'market_type': 'spot',
                    'price': 0.5 + (i * 0.001),  # 价格变化
                    'asks': [[0.51 + (i * 0.001), 1000]],
                    'bids': [[0.50 + (i * 0.001), 1000]],
                    'timestamp': int(time.time() * 1000) + i
                }
                
                try:
                    await scanner._on_market_data(test_data)
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    if error_count == 1:  # 只打印第一个错误
                        print(f"    压力测试错误示例: {str(e)}")
            
            duration = time.time() - start_time
            avg_time = (duration / 100) * 1000  # 毫秒
            
            if success_count >= 95:  # 95%成功率
                self.log_test_result('phase3_production', '高频数据处理压力', True, 
                                   f"成功率: {success_count}/100, 平均处理时间: {avg_time:.2f}ms")
            else:
                self.log_test_result('phase3_production', '高频数据处理压力', False, 
                                   f"成功率不足: {success_count}/100, 错误数: {error_count}")
                
        except Exception as e:
            self.log_test_result('phase3_production', '高频数据处理压力', False, 
                               f"压力测试异常: {str(e)}")
        
        # 测试2：并发多交易所数据处理
        try:
            from core.opportunity_scanner import OpportunityScanner
            scanner = OpportunityScanner()
            
            async def process_exchange_data(exchange, symbol_count=10):
                """处理单个交易所的多个交易对数据"""
                success = 0
                for i in range(symbol_count):
                    test_data = {
                        'exchange': exchange,
                        'symbol': f'TEST{i}-USDT',
                        'market_type': 'spot',
                        'price': 1.0 + i * 0.1,
                        'asks': [[1.1 + i * 0.1, 1000]],
                        'bids': [[1.0 + i * 0.1, 1000]],
                        'timestamp': int(time.time() * 1000) + i
                    }
                    
                    try:
                        await scanner._on_market_data(test_data)
                        success += 1
                    except:
                        pass
                return success
            
            # 并发处理三个交易所
            tasks = [
                process_exchange_data('gate', 5),
                process_exchange_data('bybit', 5),
                process_exchange_data('okx', 5)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            total_success = sum(r for r in results if isinstance(r, int))
            expected_total = 15  # 3个交易所 * 5个交易对
            
            if total_success >= expected_total * 0.9:  # 90%成功率
                self.log_test_result('phase3_production', '并发多交易所处理', True, 
                                   f"成功处理: {total_success}/{expected_total}")
            else:
                self.log_test_result('phase3_production', '并发多交易所处理', False, 
                                   f"成功率不足: {total_success}/{expected_total}")
                
        except Exception as e:
            self.log_test_result('phase3_production', '并发多交易所处理', False, 
                               f"并发测试异常: {str(e)}")
        
        # 测试3：极端边界条件测试
        try:
            from core.opportunity_scanner import OpportunityScanner
            scanner = OpportunityScanner()
            
            extreme_cases = [
                # 空数据
                {},
                # 缺失字段
                {'exchange': 'gate'},
                # 无效价格
                {'exchange': 'gate', 'symbol': 'ADA-USDT', 'price': 0},
                # 无效时间戳
                {'exchange': 'gate', 'symbol': 'ADA-USDT', 'price': 1.0, 'timestamp': -1},
                # 超大数据
                {
                    'exchange': 'gate', 
                    'symbol': 'ADA-USDT', 
                    'price': 1.0, 
                    'asks': [[i, 1000] for i in range(100)],  # 100档数据
                    'bids': [[i, 1000] for i in range(100)]
                }
            ]
            
            success_count = 0
            for i, case in enumerate(extreme_cases):
                try:
                    await scanner._on_market_data(case)
                    success_count += 1
                except Exception as e:
                    # 边界条件允许部分失败，关键是不能崩溃
                    if "cannot access local variable 'get_synced_timestamp'" in str(e):
                        # 如果还是导入错误，说明修复失败
                        raise e
            
            self.log_test_result('phase3_production', '极端边界条件', True, 
                               f"边界条件处理稳定，无导入错误: {success_count}/{len(extreme_cases)}个案例处理")
                
        except Exception as e:
            if "cannot access local variable 'get_synced_timestamp'" in str(e):
                self.log_test_result('phase3_production', '极端边界条件', False, 
                                   f"导入错误仍然存在: {str(e)}")
            else:
                self.log_test_result('phase3_production', '极端边界条件', True, 
                                   f"边界条件处理正常，无导入错误")
    
    def generate_final_report(self):
        """生成最终测试报告"""
        print("\n" + "=" * 80)
        print("🏛️ 机构级别测试最终报告")
        print("=" * 80)
        
        total_passed = 0
        total_tests = 0
        
        for phase_name, phase_data in self.test_results.items():
            passed = phase_data['passed']
            total = phase_data['total']
            percentage = (passed / total * 100) if total > 0 else 0
            
            print(f"\n📊 {phase_name.upper()}:")
            print(f"   通过率: {passed}/{total} ({percentage:.1f}%)")
            
            total_passed += passed
            total_tests += total
            
            # 显示失败的测试
            failed_tests = [t for t in phase_data['tests'] if not t['passed']]
            if failed_tests:
                print("   ❌ 失败测试:")
                for test in failed_tests:
                    print(f"      - {test['name']}: {test['details']}")
        
        overall_percentage = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n🎯 总体结果:")
        print(f"   总通过率: {total_passed}/{total_tests} ({overall_percentage:.1f}%)")
        
        # 质量评定
        if overall_percentage >= 100:
            grade = "🏆 优秀 (100%)"
            status = "✅ 可以立即部署到生产环境"
        elif overall_percentage >= 90:
            grade = "🟢 良好 (≥90%)"
            status = "✅ 可以部署，建议监控"
        elif overall_percentage >= 80:
            grade = "🟡 警告 (≥80%)"
            status = "⚠️ 需要修复失败项后部署"
        else:
            grade = f"🔴 失败 ({overall_percentage:.1f}%)"
            status = "❌ 不建议部署，需要重大修复"
        
        print(f"   质量等级: {grade}")
        print(f"   部署建议: {status}")
        
        return overall_percentage >= 90
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始机构级别导入修复验证测试")
        print("测试目标: 验证get_synced_timestamp导入修复的完整性")
        print("=" * 80)
        
        try:
            await self.phase1_basic_core_tests()
            await self.phase2_system_cascade_tests()
            await self.phase3_production_tests()
            
            return self.generate_final_report()
            
        except Exception as e:
            print(f"\n❌ 测试执行异常: {str(e)}")
            print(f"详细错误: {traceback.format_exc()}")
            return False

async def main():
    """主函数"""
    validator = CriticalImportFixValidator()
    success = await validator.run_all_tests()
    
    if success:
        print(f"\n🎉 机构级别测试通过！修复质量达标。")
        sys.exit(0)
    else:
        print(f"\n💥 机构级别测试失败！需要进一步修复。")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())