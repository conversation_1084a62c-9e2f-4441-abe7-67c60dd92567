#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳精确修复验证测试
使用当前时间戳，确保套利系统的实时性要求
"""

import sys
import os
import time
import json
from datetime import datetime
import importlib.util

# 添加项目路径
project_root = '/root/myproject/123/66B 修复时间戳问题，但是全是错误/123'
sys.path.insert(0, project_root)

# 动态导入统一时间戳处理器
spec = importlib.util.spec_from_file_location(
    "unified_timestamp_processor", 
    f"{project_root}/websocket/unified_timestamp_processor.py"
)
unified_timestamp_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(unified_timestamp_module)

# 获取所需函数和类
ensure_milliseconds_timestamp = unified_timestamp_module.ensure_milliseconds_timestamp
calculate_data_age = unified_timestamp_module.calculate_data_age
get_timestamp_processor = unified_timestamp_module.get_timestamp_processor

def test_timestamp_precision_fix():
    """测试时间戳精度修复"""
    print("🔧 测试时间戳精度修复...")
    
    current_time = time.time()
    current_time_ms = int(current_time * 1000)
    
    # 使用当前真实时间戳进行测试
    test_cases = [
        {
            "name": "当前毫秒时间戳",
            "input": current_time_ms,
            "expected_output": current_time_ms
        },
        {
            "name": "当前秒级时间戳",
            "input": int(current_time),
            "expected_output": int(current_time) * 1000
        },
        {
            "name": "1秒前毫秒时间戳",
            "input": current_time_ms - 1000,
            "expected_output": current_time_ms - 1000
        }
    ]
    
    success_count = 0
    for test_case in test_cases:
        try:
            result = ensure_milliseconds_timestamp(test_case["input"])
            
            # 验证返回类型是整数
            type_ok = isinstance(result, int)
            
            # 验证数值合理性（允许一定偏差）
            if test_case["name"] == "当前秒级时间戳":
                value_ok = abs(result - test_case["expected_output"]) <= 1000  # 1秒偏差
            else:
                value_ok = abs(result - test_case["expected_output"]) <= 100   # 100ms偏差
            
            success = type_ok and value_ok
            if success:
                success_count += 1
                print(f"✅ {test_case['name']}: {test_case['input']} → {result}")
            else:
                print(f"❌ {test_case['name']}: {test_case['input']} → {result} (期望: {test_case['expected_output']})")
                
        except Exception as e:
            print(f"❌ {test_case['name']}: 异常 {e}")
    
    print(f"📊 时间戳精度测试成功率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    return success_count == len(test_cases)

def test_data_age_calculation():
    """测试数据年龄计算"""
    print("\n🔧 测试数据年龄计算...")
    
    current_time = time.time()
    
    test_cases = [
        {
            "name": "当前时间数据",
            "data_timestamp": int(current_time * 1000),
            "current_time": current_time,
            "expected_age": 0.0,
            "tolerance": 0.1
        },
        {
            "name": "1秒前数据",
            "data_timestamp": int((current_time - 1) * 1000),
            "current_time": current_time,
            "expected_age": 1.0,
            "tolerance": 0.1
        },
        {
            "name": "500ms前数据",
            "data_timestamp": int((current_time - 0.5) * 1000),
            "current_time": current_time,
            "expected_age": 0.5,
            "tolerance": 0.1
        }
    ]
    
    success_count = 0
    for test_case in test_cases:
        try:
            calculated_age = calculate_data_age(
                test_case["data_timestamp"], 
                test_case["current_time"]
            )
            
            age_diff = abs(calculated_age - test_case["expected_age"])
            accurate = age_diff <= test_case["tolerance"]
            
            if accurate:
                success_count += 1
                print(f"✅ {test_case['name']}: 计算年龄 {calculated_age:.3f}s (期望: {test_case['expected_age']}s)")
            else:
                print(f"❌ {test_case['name']}: 计算年龄 {calculated_age:.3f}s (期望: {test_case['expected_age']}s, 误差: {age_diff:.3f}s)")
                
        except Exception as e:
            print(f"❌ {test_case['name']}: 异常 {e}")
    
    print(f"📊 数据年龄计算成功率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    return success_count == len(test_cases)

def test_timestamp_extraction():
    """测试时间戳提取功能"""
    print("\n🔧 测试时间戳提取功能...")
    
    current_time_ms = int(time.time() * 1000)
    
    # 使用当前时间戳构造测试数据
    test_data_samples = {
        "gate": [
            {"time_ms": current_time_ms, "currency_pair": "BTC-USDT"},
            {"t": int(time.time()), "currency_pair": "ETH-USDT"},  # 秒级时间戳
            {"create_time_ms": current_time_ms - 100, "side": "buy"}  # 100ms前
        ],
        "bybit": [
            {"ts": current_time_ms, "symbol": "BTCUSDT"},
            {"T": current_time_ms - 200, "side": "Buy"},  # 200ms前
            {"cts": current_time_ms - 50, "symbol": "ETHUSDT"}  # 50ms前
        ],
        "okx": [
            {"ts": str(current_time_ms), "instId": "BTC-USDT"},  # 字符串格式
            {"timestamp": int(time.time()), "instId": "ETH-USDT"}  # 秒级时间戳
        ]
    }
    
    success_count = 0
    total_tests = 0
    
    for exchange, samples in test_data_samples.items():
        try:
            processor = get_timestamp_processor(exchange)
            
            for i, sample_data in enumerate(samples):
                total_tests += 1
                try:
                    extracted_ts = processor._extract_server_timestamp_for_monitoring(sample_data)
                    
                    # 验证提取结果
                    if extracted_ts is not None:
                        type_ok = isinstance(extracted_ts, int)
                        range_ok = abs(extracted_ts - current_time_ms) <= 5000  # 5秒容忍度
                        
                        if type_ok and range_ok:
                            success_count += 1
                            print(f"✅ {exchange.upper()}-样本{i+1}: 提取时间戳 {extracted_ts} (当前: {current_time_ms})")
                        else:
                            print(f"❌ {exchange.upper()}-样本{i+1}: 时间戳类型或范围错误 {extracted_ts}")
                    else:
                        print(f"❌ {exchange.upper()}-样本{i+1}: 未能提取时间戳")
                        
                except Exception as e:
                    print(f"❌ {exchange.upper()}-样本{i+1}: 提取异常 {e}")
                    
        except Exception as e:
            print(f"❌ {exchange.upper()}: 处理器初始化异常 {e}")
    
    print(f"📊 时间戳提取成功率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    return success_count >= (total_tests * 0.8)  # 80%成功率阈值

def test_cross_exchange_sync():
    """测试跨交易所同步检查"""
    print("\n🔧 测试跨交易所同步检查...")
    
    current_time_ms = int(time.time() * 1000)
    
    test_scenarios = [
        {
            "name": "同步时间戳 (50ms差异)",
            "timestamp1": current_time_ms,
            "timestamp2": current_time_ms + 50,
            "expected_sync": True,
            "max_diff": 800
        },
        {
            "name": "临界同步 (800ms差异)",
            "timestamp1": current_time_ms,
            "timestamp2": current_time_ms + 800,
            "expected_sync": True,
            "max_diff": 800
        },
        {
            "name": "不同步 (900ms差异)",
            "timestamp1": current_time_ms,
            "timestamp2": current_time_ms + 900,
            "expected_sync": False,
            "max_diff": 800
        },
        {
            "name": "严重不同步 (5秒差异)",
            "timestamp1": current_time_ms,
            "timestamp2": current_time_ms + 5000,
            "expected_sync": False,
            "max_diff": 800
        }
    ]
    
    success_count = 0
    
    try:
        processor = get_timestamp_processor("gate")
        
        for scenario in test_scenarios:
            try:
                is_synced, time_diff = processor.validate_cross_exchange_sync(
                    scenario["timestamp1"],
                    scenario["timestamp2"],
                    "gate", "bybit",
                    max_diff_ms=scenario["max_diff"]
                )
                
                # 验证同步判断结果
                sync_correct = (is_synced == scenario["expected_sync"])
                
                # 验证时间差计算
                expected_diff = abs(scenario["timestamp1"] - scenario["timestamp2"])
                diff_accurate = abs(time_diff - expected_diff) <= 1.0
                
                success = sync_correct and diff_accurate
                if success:
                    success_count += 1
                    print(f"✅ {scenario['name']}: 同步={is_synced}, 时间差={time_diff}ms")
                else:
                    print(f"❌ {scenario['name']}: 同步={is_synced}(期望:{scenario['expected_sync']}), 时间差={time_diff}ms(期望:{expected_diff})")
                    
            except Exception as e:
                print(f"❌ {scenario['name']}: 异常 {e}")
                
    except Exception as e:
        print(f"❌ 处理器初始化异常: {e}")
    
    print(f"📊 跨交易所同步测试成功率: {success_count}/{len(test_scenarios)} ({success_count/len(test_scenarios)*100:.1f}%)")
    return success_count >= len(test_scenarios) * 0.75  # 75%成功率阈值

def main():
    """主测试函数"""
    print("🚀 开始时间戳精确修复验证...")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏰ 当前时间戳: {int(time.time() * 1000)}")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_timestamp_precision_fix())
    test_results.append(test_data_age_calculation()) 
    test_results.append(test_timestamp_extraction())
    test_results.append(test_cross_exchange_sync())
    
    # 计算总体结果
    success_count = sum(test_results)
    total_tests = len(test_results)
    success_rate = success_count / total_tests
    
    print(f"\n🏆 **修复验证总结**")
    print(f"📊 测试通过率: {success_count}/{total_tests} ({success_rate*100:.1f}%)")
    
    if success_rate >= 0.9:
        print("✅ **修复质量**: 优秀 - 可以部署")
        grade = "优秀"
    elif success_rate >= 0.75:
        print("⚠️ **修复质量**: 良好 - 需要进一步优化")
        grade = "良好"
    else:
        print("❌ **修复质量**: 不合格 - 需要重新修复")
        grade = "不合格"
    
    # 保存测试结果
    result_data = {
        "test_timestamp": datetime.now().isoformat(),
        "current_timestamp_ms": int(time.time() * 1000),
        "test_results": {
            "timestamp_precision": test_results[0],
            "data_age_calculation": test_results[1], 
            "timestamp_extraction": test_results[2],
            "cross_exchange_sync": test_results[3]
        },
        "overall_success_rate": success_rate,
        "grade": grade,
        "deployment_ready": success_rate >= 0.9
    }
    
    try:
        output_file = f"/root/myproject/123/66B 修复时间戳问题，但是全是错误/123/diagnostic_results/timestamp_fix_validation_{int(time.time())}.json"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 测试报告已保存: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存测试报告失败: {e}")
    
    return success_rate >= 0.9

if __name__ == "__main__":
    main()