#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏛️ 机构级时间戳修复验证测试
三段进阶验证机制：基础核心 → 复杂系统级联 → 生产模拟
确保修复质量达到机构标准，零缺陷部署
"""

import sys
import os
import time
import json
import asyncio
import statistics
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import importlib.util

# 添加项目路径
project_root = '/root/myproject/123/66B 修复时间戳问题，但是全是错误/123'
sys.path.insert(0, project_root)

# 动态导入统一时间戳处理器
spec = importlib.util.spec_from_file_location(
    "unified_timestamp_processor", 
    f"{project_root}/websocket/unified_timestamp_processor.py"
)
unified_timestamp_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(unified_timestamp_module)

# 获取所需函数和类
ensure_milliseconds_timestamp = unified_timestamp_module.ensure_milliseconds_timestamp
calculate_data_age = unified_timestamp_module.calculate_data_age
get_synced_timestamp = unified_timestamp_module.get_synced_timestamp
get_timestamp_processor = unified_timestamp_module.get_timestamp_processor
UnifiedTimestampProcessor = unified_timestamp_module.UnifiedTimestampProcessor


class InstitutionalTimestampValidator:
    """🏛️ 机构级时间戳验证器"""
    
    def __init__(self):
        self.results = {
            "validation_timestamp": datetime.now().isoformat(),
            "validation_type": "institutional_grade_timestamp_verification",
            "stage_1_basic_core": {"tests": [], "success_rate": 0.0},
            "stage_2_system_cascade": {"tests": [], "success_rate": 0.0},
            "stage_3_production_simulation": {"tests": [], "success_rate": 0.0},
            "overall_grade": "PENDING",
            "deployment_ready": False,
            "critical_issues": [],
            "performance_metrics": {}
        }
        
    def log_test_result(self, stage: str, test_name: str, success: bool, 
                       details: Dict, critical: bool = False):
        """记录测试结果"""
        test_result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "critical": critical,
            "timestamp": datetime.now().isoformat()
        }
        
        self.results[stage]["tests"].append(test_result)
        
        if not success and critical:
            self.results["critical_issues"].append({
                "stage": stage,
                "test": test_name,
                "issue": details
            })
        
        # 实时输出
        status = "✅" if success else "❌"
        priority = "🚨 CRITICAL" if critical and not success else ""
        print(f"{status} [{stage.upper()}] {test_name} {priority}")
        if not success:
            print(f"   错误详情: {details}")
    
    # ==================== 阶段1：基础核心测试 ====================
    
    def stage_1_basic_core_tests(self):
        """🏛️ 阶段1：基础核心测试 - 模块单元功能验证"""
        print("\n🏛️ === 阶段1：基础核心测试 ===")
        
        # 测试1：时间戳标准化函数完整性
        self._test_timestamp_standardization()
        
        # 测试2：数据年龄计算精度
        self._test_data_age_precision()
        
        # 测试3：边界条件处理
        self._test_boundary_conditions()
        
        # 测试4：错误处理机制
        self._test_error_handling()
        
        # 测试5：时间戳提取器修复验证
        self._test_timestamp_extraction_fix()
        
        # 计算成功率
        stage_tests = self.results["stage_1_basic_core"]["tests"]
        success_count = sum(1 for t in stage_tests if t["success"])
        self.results["stage_1_basic_core"]["success_rate"] = success_count / len(stage_tests) if stage_tests else 0
        
        print(f"📊 阶段1成功率: {self.results['stage_1_basic_core']['success_rate']*100:.1f}% ({success_count}/{len(stage_tests)})")
    
    def _test_timestamp_standardization(self):
        """测试时间戳标准化函数"""
        test_cases = [
            # (输入, 预期输出类型, 描述)
            (1754058178612, int, "标准毫秒时间戳整数化"),
            (1754058178.612, int, "浮点毫秒时间戳整数化"),
            (1754058178, int, "秒级时间戳转毫秒"),
            (1754058178.0, int, "浮点秒级时间戳转毫秒"),
            (1754058178612000000, int, "纳秒时间戳转毫秒"),
            (None, int, "None值处理"),
            (0, int, "零值处理"),
            (-1, int, "负值处理")
        ]
        
        for input_ts, expected_type, description in test_cases:
            try:
                result = ensure_milliseconds_timestamp(input_ts)
                
                # 验证返回类型
                type_correct = isinstance(result, int)
                
                # 验证时间戳合理性（应该是13位毫秒时间戳）
                reasonable = 1e12 < result < 2e12 if result > 0 else True
                
                # 验证整数特性（无小数部分）
                is_integer = result == int(result)
                
                success = type_correct and reasonable and is_integer
                
                self.log_test_result(
                    "stage_1_basic_core",
                    f"时间戳标准化: {description}",
                    success,
                    {
                        "input": input_ts,
                        "output": result,
                        "output_type": type(result).__name__,
                        "expected_type": expected_type.__name__,
                        "type_correct": type_correct,
                        "reasonable_range": reasonable,
                        "is_integer": is_integer
                    },
                    critical=True  # 时间戳标准化是关键功能
                )
                
            except Exception as e:
                self.log_test_result(
                    "stage_1_basic_core",
                    f"时间戳标准化异常: {description}",
                    False,
                    {"input": input_ts, "error": str(e)},
                    critical=True
                )
    
    def _test_data_age_precision(self):
        """测试数据年龄计算精度"""
        current_time = time.time()
        
        test_cases = [
            # (数据时间戳, 当前时间, 预期年龄秒数, 容忍误差, 描述)
            (int((current_time - 1) * 1000), current_time, 1.0, 0.01, "1秒前毫秒数据"),
            (int((current_time - 0.5) * 1000), current_time, 0.5, 0.01, "500ms前毫秒数据"),
            (int(current_time - 1), current_time, 1.0, 0.01, "1秒前秒级数据"),
            (int(current_time - 5), current_time, 5.0, 0.01, "5秒前秒级数据"),
            # 🔥 新增：测试浮点数时间戳处理
            ((current_time - 1.5) * 1000, current_time, 1.5, 0.01, "1.5秒前浮点毫秒数据")
        ]
        
        for data_ts, current_t, expected_age, tolerance, description in test_cases:
            try:
                calculated_age = calculate_data_age(data_ts, current_t)
                age_diff = abs(calculated_age - expected_age)
                precision_ok = age_diff <= tolerance
                
                self.log_test_result(
                    "stage_1_basic_core",
                    f"数据年龄计算: {description}",
                    precision_ok,
                    {
                        "data_timestamp": data_ts,
                        "expected_age": expected_age,
                        "calculated_age": calculated_age,
                        "age_difference": age_diff,
                        "tolerance": tolerance,
                        "precision_ok": precision_ok
                    },
                    critical=True  # 年龄计算精度是关键
                )
                
            except Exception as e:
                self.log_test_result(
                    "stage_1_basic_core",
                    f"数据年龄计算异常: {description}",
                    False,
                    {"error": str(e)},
                    critical=True
                )
    
    def _test_boundary_conditions(self):
        """测试边界条件处理"""
        boundary_cases = [
            # (输入, 描述, 应该成功)
            (float('inf'), "无穷大值", False),
            (float('-inf'), "负无穷大值", False),
            (float('nan'), "NaN值", False),
            (1e20, "超大数值", True),  # 应该能处理并转换
            (1, "极小正数", True),
            ("invalid", "无效字符串", False)
        ]
        
        for input_val, description, should_succeed in boundary_cases:
            try:
                result = ensure_milliseconds_timestamp(input_val)
                
                # 验证结果合理性
                if should_succeed:
                    success = isinstance(result, int) and result > 0
                else:
                    # 对于无效输入，应该返回当前时间戳
                    success = isinstance(result, int) and 1e12 < result < 2e12
                
                self.log_test_result(
                    "stage_1_basic_core",
                    f"边界条件: {description}",
                    success,
                    {
                        "input": str(input_val),
                        "result": result,
                        "should_succeed": should_succeed,
                        "actual_success": success
                    }
                )
                
            except Exception as e:
                # 异常也可能是合理的处理方式
                success = not should_succeed  # 如果不应该成功，异常是合理的
                self.log_test_result(
                    "stage_1_basic_core",
                    f"边界条件异常: {description}",
                    success,
                    {"input": str(input_val), "error": str(e)}
                )
    
    def _test_error_handling(self):
        """测试错误处理机制"""
        print("   测试错误处理机制...")
        
        # 测试时间戳处理器异常处理
        try:
            processor = get_timestamp_processor("invalid_exchange")
            # 应该返回有效的处理器或抛出合理异常
            success = processor is not None
            
            self.log_test_result(
                "stage_1_basic_core",
                "无效交易所处理器",
                success,
                {"processor_returned": processor is not None}
            )
            
        except Exception as e:
            # 异常是合理的
            self.log_test_result(
                "stage_1_basic_core",
                "无效交易所异常处理",
                True,
                {"expected_exception": str(e)}
            )
    
    def _test_timestamp_extraction_fix(self):
        """🔥 测试时间戳提取器修复效果"""
        print("   🔥 测试时间戳提取器修复...")
        
        # 模拟Gate.io WebSocket数据
        gate_data_samples = [
            {"time_ms": 1754058178612, "price": "50000"},  # 毫秒时间戳
            {"t": 1754058178, "price": "50000"},  # 秒级时间戳
            {"create_time_ms": 1754058178612, "side": "buy"},  # 交易数据
            {"timestamp": 1754058178, "symbol": "BTC-USDT"},  # 备用时间戳
        ]
        
        # 模拟Bybit WebSocket数据
        bybit_data_samples = [
            {"ts": 1754058178612, "price": "50000"},  # 毫秒时间戳
            {"T": 1754058178612, "side": "Buy"},  # 交易时间戳
            {"cts": 1754058178612, "symbol": "BTCUSDT"},  # 创建时间戳
        ]
        
        # 模拟OKX WebSocket数据
        okx_data_samples = [
            {"ts": "1754058178612", "px": "50000"},  # 字符串毫秒时间戳
            {"timestamp": 1754058178, "instId": "BTC-USDT"},  # 备用时间戳
        ]
        
        test_samples = [
            ("gate", gate_data_samples),
            ("bybit", bybit_data_samples),
            ("okx", okx_data_samples)
        ]
        
        for exchange, samples in test_samples:
            try:
                processor = get_timestamp_processor(exchange)
                
                for i, sample_data in enumerate(samples):
                    try:
                        # 测试时间戳提取
                        extracted_ts = processor._extract_server_timestamp_for_monitoring(sample_data)
                        
                        # 验证结果
                        valid_extraction = (
                            extracted_ts is not None and 
                            isinstance(extracted_ts, int) and  # 🔥 确保返回整数
                            1e12 < extracted_ts < 2e12  # 合理的毫秒时间戳范围
                        )
                        
                        self.log_test_result(
                            "stage_1_basic_core",
                            f"{exchange.upper()}时间戳提取-样本{i+1}",
                            valid_extraction,
                            {
                                "sample_data": sample_data,
                                "extracted_timestamp": extracted_ts,
                                "is_integer": isinstance(extracted_ts, int) if extracted_ts else False,
                                "in_valid_range": 1e12 < extracted_ts < 2e12 if extracted_ts else False
                            },
                            critical=True  # 时间戳提取是关键功能
                        )
                        
                    except Exception as e:
                        self.log_test_result(
                            "stage_1_basic_core",
                            f"{exchange.upper()}时间戳提取异常-样本{i+1}",
                            False,
                            {"sample_data": sample_data, "error": str(e)},
                            critical=True
                        )
                        
            except Exception as e:
                self.log_test_result(
                    "stage_1_basic_core",
                    f"{exchange.upper()}处理器初始化异常",
                    False,
                    {"error": str(e)},
                    critical=True
                )
    
    # ==================== 阶段2：复杂系统级联测试 ====================
    
    def stage_2_system_cascade_tests(self):
        """🏛️ 阶段2：复杂系统级联测试 - 多模块交互验证"""
        print("\n🏛️ === 阶段2：复杂系统级联测试 ===")
        
        # 测试1：跨交易所时间戳同步一致性
        self._test_cross_exchange_sync_consistency()
        
        # 测试2：多币种时间戳处理一致性
        self._test_multi_symbol_consistency()
        
        # 测试3：状态联动测试
        self._test_state_coordination()
        
        # 测试4：链路完整性测试
        self._test_pipeline_integrity()
        
        # 计算成功率
        stage_tests = self.results["stage_2_system_cascade"]["tests"]
        success_count = sum(1 for t in stage_tests if t["success"])
        self.results["stage_2_system_cascade"]["success_rate"] = success_count / len(stage_tests) if stage_tests else 0
        
        print(f"📊 阶段2成功率: {self.results['stage_2_system_cascade']['success_rate']*100:.1f}% ({success_count}/{len(stage_tests)})")
    
    def _test_cross_exchange_sync_consistency(self):
        """测试跨交易所时间戳同步一致性"""
        print("   测试跨交易所同步一致性...")
        
        exchanges = ["gate", "bybit", "okx"]
        current_time = int(time.time() * 1000)
        
        # 生成模拟时间戳（模拟正常的网络延迟范围）
        simulated_timestamps = {
            "gate": current_time + 50,     # Gate稍快50ms
            "bybit": current_time - 30,    # Bybit稍慢30ms  
            "okx": current_time + 20       # OKX稍快20ms
        }
        
        # 测试两两交易所同步检查
        sync_results = []
        for i, ex1 in enumerate(exchanges):
            for ex2 in exchanges[i+1:]:
                try:
                    processor = get_timestamp_processor(ex1)
                    is_synced, time_diff = processor.validate_cross_exchange_sync(
                        simulated_timestamps[ex1],
                        simulated_timestamps[ex2],
                        ex1, ex2,
                        max_diff_ms=800  # 标准800ms阈值
                    )
                    
                    # 计算预期时间差
                    expected_diff = abs(simulated_timestamps[ex1] - simulated_timestamps[ex2])
                    diff_accurate = abs(time_diff - expected_diff) < 1.0  # 1ms误差容忍
                    
                    # 验证同步判断是否正确
                    should_sync = expected_diff <= 800
                    sync_correct = (is_synced == should_sync)
                    
                    success = diff_accurate and sync_correct
                    sync_results.append(success)
                    
                    self.log_test_result(
                        "stage_2_system_cascade",
                        f"跨交易所同步: {ex1.upper()}-{ex2.upper()}",
                        success,
                        {
                            "exchange_1": ex1,
                            "exchange_2": ex2,
                            "timestamp_1": simulated_timestamps[ex1],
                            "timestamp_2": simulated_timestamps[ex2],
                            "expected_diff": expected_diff,
                            "calculated_diff": time_diff,
                            "is_synced": is_synced,
                            "should_sync": should_sync,
                            "diff_accurate": diff_accurate,
                            "sync_correct": sync_correct
                        },
                        critical=True  # 跨交易所同步是关键
                    )
                    
                except Exception as e:
                    sync_results.append(False)
                    self.log_test_result(
                        "stage_2_system_cascade",
                        f"跨交易所同步异常: {ex1.upper()}-{ex2.upper()}",
                        False,
                        {"error": str(e)},
                        critical=True
                    )
        
        # 总体同步一致性评估
        overall_sync_success = all(sync_results) if sync_results else False
        self.log_test_result(
            "stage_2_system_cascade",
            "总体跨交易所同步一致性",
            overall_sync_success,
            {
                "total_pairs": len(sync_results),
                "successful_pairs": sum(sync_results),
                "success_rate": sum(sync_results) / len(sync_results) if sync_results else 0
            },
            critical=True
        )
    
    def _test_multi_symbol_consistency(self):
        """测试多币种时间戳处理一致性"""
        print("   测试多币种一致性...")
        
        # 模拟多币种数据
        symbols = ["BTC-USDT", "ETH-USDT", "SOL-USDT", "DOGE-USDT"]
        base_timestamp = int(time.time() * 1000)
        
        consistency_results = []
        
        for symbol in symbols:
            # 模拟每个币种的WebSocket数据
            mock_data = {
                "symbol": symbol,
                "time_ms": base_timestamp + hash(symbol) % 100,  # 轻微时间差异
                "price": "50000",
                "volume": "1.0"
            }
            
            try:
                # 测试三个交易所对相同币种的时间戳处理
                exchange_timestamps = {}
                
                for exchange in ["gate", "bybit", "okx"]:
                    processor = get_timestamp_processor(exchange)
                    
                    # 根据交易所调整数据格式
                    if exchange == "gate":
                        exchange_data = {"time_ms": mock_data["time_ms"], "currency_pair": symbol}
                    elif exchange == "bybit":
                        exchange_data = {"ts": mock_data["time_ms"], "symbol": symbol.replace("-", "")}
                    else:  # okx
                        exchange_data = {"ts": str(mock_data["time_ms"]), "instId": symbol}
                    
                    timestamp = processor._extract_server_timestamp_for_monitoring(exchange_data)
                    exchange_timestamps[exchange] = timestamp
                
                # 验证一致性：所有交易所应该返回相同的时间戳（或合理差异）
                timestamps = list(exchange_timestamps.values())
                valid_timestamps = [ts for ts in timestamps if ts is not None]
                
                if len(valid_timestamps) >= 2:
                    # 计算时间戳差异
                    max_diff = max(valid_timestamps) - min(valid_timestamps)
                    consistency_ok = max_diff <= 100  # 100ms容忍度
                    
                    consistency_results.append(consistency_ok)
                    
                    self.log_test_result(
                        "stage_2_system_cascade",
                        f"多币种一致性: {symbol}",
                        consistency_ok,
                        {
                            "symbol": symbol,
                            "exchange_timestamps": exchange_timestamps,
                            "max_difference_ms": max_diff,
                            "consistency_threshold": 100,
                            "consistent": consistency_ok
                        }
                    )
                else:
                    consistency_results.append(False)
                    self.log_test_result(
                        "stage_2_system_cascade",
                        f"多币种提取失败: {symbol}",
                        False,
                        {"symbol": symbol, "valid_timestamps": len(valid_timestamps)}
                    )
                    
            except Exception as e:
                consistency_results.append(False)
                self.log_test_result(
                    "stage_2_system_cascade",
                    f"多币种异常: {symbol}",
                    False,
                    {"symbol": symbol, "error": str(e)}
                )
        
        # 总体一致性评估
        overall_consistency = all(consistency_results) if consistency_results else False
        self.log_test_result(
            "stage_2_system_cascade",
            "总体多币种一致性",
            overall_consistency,
            {
                "total_symbols": len(symbols),
                "consistent_symbols": sum(consistency_results),
                "consistency_rate": sum(consistency_results) / len(consistency_results) if consistency_results else 0
            },
            critical=True
        )
    
    def _test_state_coordination(self):
        """测试状态联动"""
        print("   测试状态联动...")
        
        # 测试时间同步状态与时间戳生成的联动
        try:
            processor = get_timestamp_processor("gate")
            
            # 测试未同步状态
            original_sync_state = processor.time_synced
            processor.time_synced = False
            
            timestamp_unsync = processor.get_synced_timestamp()
            
            # 测试已同步状态
            processor.time_synced = True
            processor.time_offset = 50  # 50ms偏移
            
            timestamp_sync = processor.get_synced_timestamp()
            
            # 恢复原始状态
            processor.time_synced = original_sync_state
            
            # 验证状态联动是否正确
            # 两个时间戳应该有差异（体现同步状态的影响）
            state_coordination_ok = abs(timestamp_sync - timestamp_unsync) >= 40  # 至少40ms差异
            
            self.log_test_result(
                "stage_2_system_cascade",
                "时间同步状态联动",
                state_coordination_ok,
                {
                    "timestamp_unsync": timestamp_unsync,
                    "timestamp_sync": timestamp_sync,
                    "difference_ms": abs(timestamp_sync - timestamp_unsync),
                    "coordination_ok": state_coordination_ok
                }
            )
            
        except Exception as e:
            self.log_test_result(
                "stage_2_system_cascade",
                "状态联动异常",
                False,
                {"error": str(e)}
            )
    
    def _test_pipeline_integrity(self):
        """测试链路完整性"""
        print("   测试处理链路完整性...")
        
        # 测试完整的数据处理链路：原始数据 → 时间戳提取 → 标准化 → 年龄计算
        test_data = {
            "time_ms": int((time.time() - 0.5) * 1000),  # 500ms前的数据
            "symbol": "BTC-USDT",
            "price": "50000"
        }
        
        try:
            processor = get_timestamp_processor("gate")
            current_time = time.time()
            
            # 1. 时间戳提取
            extracted_ts = processor._extract_server_timestamp_for_monitoring(test_data)
            extract_success = extracted_ts is not None and isinstance(extracted_ts, int)
            
            # 2. 时间戳标准化
            if extracted_ts:
                standardized_ts = ensure_milliseconds_timestamp(extracted_ts)
                standardize_success = isinstance(standardized_ts, int)
            else:
                standardized_ts = None
                standardize_success = False
            
            # 3. 数据年龄计算
            if standardized_ts:
                data_age = calculate_data_age(standardized_ts, current_time)
                age_success = isinstance(data_age, float) and 0 <= data_age <= 1.0
            else:
                data_age = None
                age_success = False
            
            # 4. 同步时间戳生成
            synced_ts = processor.get_synced_timestamp(test_data)
            sync_success = isinstance(synced_ts, int)
            
            # 链路完整性验证
            pipeline_integrity = all([extract_success, standardize_success, age_success, sync_success])
            
            self.log_test_result(
                "stage_2_system_cascade",
                "处理链路完整性",
                pipeline_integrity,
                {
                    "step_1_extract": {"success": extract_success, "result": extracted_ts},
                    "step_2_standardize": {"success": standardize_success, "result": standardized_ts},
                    "step_3_age_calc": {"success": age_success, "result": data_age},
                    "step_4_sync_gen": {"success": sync_success, "result": synced_ts},
                    "pipeline_complete": pipeline_integrity
                },
                critical=True  # 链路完整性是关键
            )
            
        except Exception as e:
            self.log_test_result(
                "stage_2_system_cascade",
                "链路完整性异常",
                False,
                {"error": str(e)},
                critical=True
            )
    
    # ==================== 阶段3：生产模拟测试 ====================
    
    async def stage_3_production_simulation(self):
        """🏛️ 阶段3：生产模拟测试 - 真实环境模拟"""
        print("\n🏛️ === 阶段3：生产模拟测试 ===")
        
        # 测试1：真实API时间戳测试
        await self._test_real_api_timestamps()
        
        # 测试2：网络波动模拟
        self._test_network_fluctuation_simulation()
        
        # 测试3：多任务并发压力测试
        await self._test_concurrent_pressure()
        
        # 测试4：极限场景测试
        self._test_extreme_scenarios()
        
        # 计算成功率
        stage_tests = self.results["stage_3_production_simulation"]["tests"]
        success_count = sum(1 for t in stage_tests if t["success"])
        self.results["stage_3_production_simulation"]["success_rate"] = success_count / len(stage_tests) if stage_tests else 0
        
        print(f"📊 阶段3成功率: {self.results['stage_3_production_simulation']['success_rate']*100:.1f}% ({success_count}/{len(stage_tests)})")
    
    async def _test_real_api_timestamps(self):
        """测试真实API时间戳"""
        print("   测试真实API时间戳...")
        
        import aiohttp
        
        # 三个交易所的时间API
        api_configs = {
            "gate": "https://api.gateio.ws/api/v4/spot/time",
            "bybit": "https://api.bybit.com/v5/market/time",
            "okx": "https://www.okx.com/api/v5/public/time"
        }
        
        api_results = []
        
        for exchange, api_url in api_configs.items():
            try:
                processor = get_timestamp_processor(exchange)
                
                async with aiohttp.ClientSession() as session:
                    start_time = time.time()
                    async with session.get(api_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                        end_time = time.time()
                        
                        if response.status == 200:
                            data = await response.json()
                            network_latency = (end_time - start_time) * 1000
                            
                            # 使用处理器提取时间戳
                            server_timestamp = processor._extract_server_time(data)
                            
                            if server_timestamp:
                                # 验证时间戳格式和合理性
                                is_integer = isinstance(server_timestamp, int)
                                is_reasonable = 1e12 < server_timestamp < 2e12
                                
                                # 计算时间差异
                                local_time = int(time.time() * 1000)
                                time_diff = abs(server_timestamp - local_time)
                                
                                success = is_integer and is_reasonable and time_diff < 5000  # 5秒容忍
                                api_results.append(success)
                                
                                self.log_test_result(
                                    "stage_3_production_simulation",
                                    f"真实API时间戳: {exchange.upper()}",
                                    success,
                                    {
                                        "server_timestamp": server_timestamp,
                                        "local_timestamp": local_time,
                                        "time_difference_ms": time_diff,
                                        "network_latency_ms": network_latency,
                                        "is_integer": is_integer,
                                        "is_reasonable": is_reasonable,
                                        "api_url": api_url
                                    },
                                    critical=True
                                )
                            else:
                                api_results.append(False)
                                self.log_test_result(
                                    "stage_3_production_simulation",
                                    f"API时间戳提取失败: {exchange.upper()}",
                                    False,
                                    {"api_response": data},
                                    critical=True
                                )
                        else:
                            api_results.append(False)
                            self.log_test_result(
                                "stage_3_production_simulation",
                                f"API请求失败: {exchange.upper()}",
                                False,
                                {"status_code": response.status}
                            )
                            
            except asyncio.TimeoutError:
                api_results.append(False)
                self.log_test_result(
                    "stage_3_production_simulation",
                    f"API请求超时: {exchange.upper()}",
                    False,
                    {"timeout": "5s"}
                )
            except Exception as e:
                api_results.append(False)
                self.log_test_result(
                    "stage_3_production_simulation",
                    f"API测试异常: {exchange.upper()}",
                    False,
                    {"error": str(e)}
                )
        
        # 总体API测试结果
        overall_api_success = all(api_results) if api_results else False
        self.log_test_result(
            "stage_3_production_simulation",
            "总体真实API测试",
            overall_api_success,
            {
                "total_apis": len(api_configs),
                "successful_apis": sum(api_results),
                "success_rate": sum(api_results) / len(api_results) if api_results else 0
            },
            critical=True
        )
    
    def _test_network_fluctuation_simulation(self):
        """测试网络波动模拟"""
        print("   模拟网络波动情况...")
        
        # 模拟各种网络延迟情况下的时间戳处理
        network_scenarios = [
            (0, "无延迟"),
            (50, "低延迟50ms"),
            (200, "中等延迟200ms"),
            (800, "高延迟800ms"),
            (1500, "极高延迟1.5s")
        ]
        
        fluctuation_results = []
        base_time = int(time.time() * 1000)
        
        for delay_ms, scenario_desc in network_scenarios:
            try:
                # 模拟网络延迟导致的时间戳差异
                simulated_server_time = base_time - delay_ms  # 服务器时间较旧
                simulated_local_time = base_time
                
                # 使用数据年龄计算函数验证
                data_age = calculate_data_age(simulated_server_time, simulated_local_time / 1000)
                expected_age = delay_ms / 1000.0
                
                age_accurate = abs(data_age - expected_age) < 0.01  # 10ms误差容忍
                
                # 测试跨交易所同步在此延迟下的表现
                processor = get_timestamp_processor("gate")
                is_synced, time_diff = processor.validate_cross_exchange_sync(
                    simulated_local_time,
                    simulated_server_time,
                    "gate", "bybit",
                    max_diff_ms=800
                )
                
                sync_correct = (is_synced == (delay_ms <= 800))
                
                success = age_accurate and sync_correct
                fluctuation_results.append(success)
                
                self.log_test_result(
                    "stage_3_production_simulation",
                    f"网络波动模拟: {scenario_desc}",
                    success,
                    {
                        "simulated_delay_ms": delay_ms,
                        "calculated_age_s": data_age,
                        "expected_age_s": expected_age,
                        "age_accurate": age_accurate,
                        "sync_result": is_synced,
                        "sync_correct": sync_correct,
                        "time_diff_ms": time_diff
                    }
                )
                
            except Exception as e:
                fluctuation_results.append(False)
                self.log_test_result(
                    "stage_3_production_simulation",
                    f"网络波动异常: {scenario_desc}",
                    False,
                    {"error": str(e)}
                )
        
        # 网络适应性总体评估
        network_adaptability = all(fluctuation_results) if fluctuation_results else False
        self.log_test_result(
            "stage_3_production_simulation",
            "网络波动适应性",
            network_adaptability,
            {
                "total_scenarios": len(network_scenarios),
                "successful_scenarios": sum(fluctuation_results),
                "adaptability_rate": sum(fluctuation_results) / len(fluctuation_results) if fluctuation_results else 0
            },
            critical=True
        )
    
    async def _test_concurrent_pressure(self):
        """测试多任务并发压力"""
        print("   执行并发压力测试...")
        
        # 模拟高频时间戳处理请求
        async def timestamp_processing_task(task_id: int, exchange: str):
            """单个时间戳处理任务"""
            try:
                processor = get_timestamp_processor(exchange)
                
                # 模拟高频数据
                tasks_results = []
                for i in range(100):  # 每个任务处理100次
                    mock_data = {
                        "time_ms": int(time.time() * 1000) + i,
                        "task_id": task_id,
                        "iteration": i
                    }
                    
                    timestamp = processor.get_synced_timestamp(mock_data)
                    age = calculate_data_age(timestamp)
                    
                    # 验证结果合理性
                    valid_result = (
                        isinstance(timestamp, int) and
                        isinstance(age, float) and
                        timestamp > 1e12 and
                        age >= 0
                    )
                    tasks_results.append(valid_result)
                
                return {
                    "task_id": task_id,
                    "exchange": exchange,
                    "success_rate": sum(tasks_results) / len(tasks_results),
                    "total_operations": len(tasks_results)
                }
                
            except Exception as e:
                return {
                    "task_id": task_id,
                    "exchange": exchange,
                    "error": str(e),
                    "success_rate": 0.0
                }
        
        # 启动并发任务
        concurrent_tasks = []
        for i in range(10):  # 10个并发任务
            exchange = ["gate", "bybit", "okx"][i % 3]  # 轮换交易所
            task = timestamp_processing_task(i, exchange)
            concurrent_tasks.append(task)
        
        try:
            # 测量并发执行时间
            start_time = time.time()
            results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
            end_time = time.time()
            
            execution_time = end_time - start_time
            
            # 分析结果
            successful_tasks = []
            failed_tasks = []
            
            for result in results:
                if isinstance(result, dict) and "success_rate" in result:
                    if result["success_rate"] >= 0.95:  # 95%成功率阈值
                        successful_tasks.append(result)
                    else:
                        failed_tasks.append(result)
                else:
                    failed_tasks.append({"error": str(result)})
            
            # 计算总体性能指标
            total_operations = sum(r.get("total_operations", 0) for r in successful_tasks)
            operations_per_second = total_operations / execution_time if execution_time > 0 else 0
            
            # 并发测试成功条件：
            # 1. 至少80%的任务成功
            # 2. 执行时间合理（不超过30秒）
            # 3. 性能达标（>1000操作/秒）
            
            success_rate = len(successful_tasks) / len(results)
            performance_ok = operations_per_second >= 1000
            time_reasonable = execution_time <= 30
            
            concurrent_success = (success_rate >= 0.8) and performance_ok and time_reasonable
            
            self.log_test_result(
                "stage_3_production_simulation",
                "并发压力测试",
                concurrent_success,
                {
                    "total_tasks": len(results),
                    "successful_tasks": len(successful_tasks),
                    "failed_tasks": len(failed_tasks),
                    "success_rate": success_rate,
                    "execution_time_s": execution_time,
                    "total_operations": total_operations,
                    "operations_per_second": operations_per_second,
                    "performance_threshold": 1000,
                    "performance_ok": performance_ok,
                    "time_reasonable": time_reasonable
                },
                critical=True
            )
            
            # 记录性能指标
            self.results["performance_metrics"]["concurrent_processing"] = {
                "operations_per_second": operations_per_second,
                "concurrent_tasks": len(results),
                "success_rate": success_rate,
                "execution_time": execution_time
            }
            
        except Exception as e:
            self.log_test_result(
                "stage_3_production_simulation",
                "并发压力测试异常",
                False,
                {"error": str(e)},
                critical=True
            )
    
    def _test_extreme_scenarios(self):
        """测试极限场景"""
        print("   测试极限场景...")
        
        extreme_scenarios = [
            # 极大时间戳差异（模拟系统时钟严重不同步）
            {
                "name": "极大时间戳差异",
                "timestamp1": int(time.time() * 1000),
                "timestamp2": int(time.time() * 1000) - 60000,  # 1分钟差异
                "should_sync": False,
                "critical": True
            },
            
            # 边界值测试
            {
                "name": "边界值800ms",
                "timestamp1": int(time.time() * 1000),
                "timestamp2": int(time.time() * 1000) - 800,  # 恰好800ms
                "should_sync": True,
                "critical": True
            },
            
            # 边界值测试（超出阈值1ms）
            {
                "name": "边界值801ms",
                "timestamp1": int(time.time() * 1000),
                "timestamp2": int(time.time() * 1000) - 801,  # 超出1ms
                "should_sync": False,
                "critical": True
            },
            
            # 极小时间差
            {
                "name": "极小时间差1ms",
                "timestamp1": int(time.time() * 1000),
                "timestamp2": int(time.time() * 1000) - 1,  # 1ms差异
                "should_sync": True,
                "critical": False
            }
        ]
        
        extreme_results = []
        
        for scenario in extreme_scenarios:
            try:
                processor = get_timestamp_processor("gate")
                
                is_synced, time_diff = processor.validate_cross_exchange_sync(
                    scenario["timestamp1"],
                    scenario["timestamp2"],
                    "gate", "bybit",
                    max_diff_ms=800
                )
                
                # 验证结果是否符合预期
                result_correct = (is_synced == scenario["should_sync"])
                
                # 验证时间差计算是否准确
                expected_diff = abs(scenario["timestamp1"] - scenario["timestamp2"])
                diff_accurate = abs(time_diff - expected_diff) < 1.0
                
                success = result_correct and diff_accurate
                extreme_results.append(success)
                
                self.log_test_result(
                    "stage_3_production_simulation",
                    f"极限场景: {scenario['name']}",
                    success,
                    {
                        "timestamp_1": scenario["timestamp1"],
                        "timestamp_2": scenario["timestamp2"],
                        "expected_diff": expected_diff,
                        "calculated_diff": time_diff,
                        "should_sync": scenario["should_sync"],
                        "actual_sync": is_synced,
                        "result_correct": result_correct,
                        "diff_accurate": diff_accurate
                    },
                    critical=scenario["critical"]
                )
                
            except Exception as e:
                extreme_results.append(False)
                self.log_test_result(
                    "stage_3_production_simulation",
                    f"极限场景异常: {scenario['name']}",
                    False,
                    {"error": str(e)},
                    critical=scenario["critical"]
                )
        
        # 极限场景适应性评估
        extreme_adaptability = all(extreme_results) if extreme_results else False
        self.log_test_result(
            "stage_3_production_simulation",
            "极限场景适应性",
            extreme_adaptability,
            {
                "total_scenarios": len(extreme_scenarios),
                "successful_scenarios": sum(extreme_results),
                "adaptability_rate": sum(extreme_results) / len(extreme_results) if extreme_results else 0
            },
            critical=True
        )
    
    # ==================== 综合评估 ====================
    
    def generate_institutional_grade_report(self):
        """生成机构级验证报告"""
        print("\n🏛️ === 机构级验证报告 ===")
        
        # 计算各阶段成功率
        stage1_rate = self.results["stage_1_basic_core"]["success_rate"]
        stage2_rate = self.results["stage_2_system_cascade"]["success_rate"]
        stage3_rate = self.results["stage_3_production_simulation"]["success_rate"]
        
        # 计算总体成功率（加权平均）
        overall_rate = (stage1_rate * 0.3 + stage2_rate * 0.3 + stage3_rate * 0.4)
        
        # 统计关键问题
        critical_issues_count = len(self.results["critical_issues"])
        
        # 机构级评级标准
        if overall_rate >= 0.95 and critical_issues_count == 0:
            grade = "A+ 机构级卓越"
            deployment_ready = True
        elif overall_rate >= 0.90 and critical_issues_count <= 1:
            grade = "A 机构级优秀"
            deployment_ready = True
        elif overall_rate >= 0.85 and critical_issues_count <= 2:
            grade = "B+ 企业级良好"
            deployment_ready = True
        elif overall_rate >= 0.80:
            grade = "B 企业级合格"
            deployment_ready = False
        elif overall_rate >= 0.70:
            grade = "C 基础级"
            deployment_ready = False
        else:
            grade = "D 不合格"
            deployment_ready = False
        
        self.results["overall_grade"] = grade
        self.results["deployment_ready"] = deployment_ready
        
        # 输出报告
        print(f"📊 **验证结果总览**:")
        print(f"   🏛️ 阶段1（基础核心测试）: {stage1_rate*100:.1f}%")
        print(f"   🏛️ 阶段2（系统级联测试）: {stage2_rate*100:.1f}%")
        print(f"   🏛️ 阶段3（生产模拟测试）: {stage3_rate*100:.1f}%")
        print(f"   📈 **总体成功率**: {overall_rate*100:.1f}%")
        print(f"   🏆 **机构级评级**: {grade}")
        print(f"   🚀 **部署就绪**: {'✅ 是' if deployment_ready else '❌ 否'}")
        
        if critical_issues_count > 0:
            print(f"\n🚨 **关键问题** ({critical_issues_count}个):")
            for issue in self.results["critical_issues"]:
                print(f"   - [{issue['stage']}] {issue['test']}: {issue['issue']}")
        
        # 性能指标汇总
        if self.results["performance_metrics"]:
            print(f"\n⚡ **性能指标**:")
            for metric_name, metric_value in self.results["performance_metrics"].items():
                if isinstance(metric_value, dict):
                    print(f"   {metric_name}:")
                    for k, v in metric_value.items():
                        print(f"     - {k}: {v}")
                else:
                    print(f"   {metric_name}: {metric_value}")
        
        return {
            "overall_success_rate": overall_rate,
            "grade": grade,
            "deployment_ready": deployment_ready,
            "critical_issues": critical_issues_count,
            "detailed_results": self.results
        }
    
    def save_validation_report(self):
        """保存验证报告"""
        timestamp = int(time.time())
        output_file = f"/root/myproject/123/66B 修复时间戳问题，但是全是错误/123/diagnostic_results/institutional_timestamp_validation_{timestamp}.json"
        
        try:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"📄 机构级验证报告已保存: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"❌ 保存验证报告失败: {e}")
            return None
    
    async def run_institutional_validation(self):
        """运行完整的机构级验证"""
        print("🏛️ 开始机构级时间戳修复验证...")
        print(f"📅 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 阶段1：基础核心测试
        self.stage_1_basic_core_tests()
        
        # 阶段2：复杂系统级联测试
        self.stage_2_system_cascade_tests()
        
        # 阶段3：生产模拟测试
        await self.stage_3_production_simulation()
        
        # 生成机构级报告
        final_report = self.generate_institutional_grade_report()
        
        # 保存报告
        report_file = self.save_validation_report()
        
        print(f"\n🏆 **机构级验证完成!**")
        print(f"🎯 总体评级: {final_report['grade']}")
        print(f"🚀 部署状态: {'可立即部署' if final_report['deployment_ready'] else '需要修复后部署'}")
        
        return final_report


async def main():
    """主函数"""
    validator = InstitutionalTimestampValidator()
    await validator.run_institutional_validation()


if __name__ == "__main__":
    asyncio.run(main())