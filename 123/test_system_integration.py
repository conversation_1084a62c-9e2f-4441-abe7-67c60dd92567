#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统级联测试：多交易所协同工作验证
验证模块间交互逻辑、状态联动、多交易所分支
"""

import asyncio
import sys
import os
import time
import json
from typing import Dict, Any, List

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SystemIntegrationTest:
    """系统级联测试器"""
    
    def __init__(self):
        self.test_results = {
            'multi_exchange_instantiation': [],
            'timestamp_sync_coordination': [],
            'connection_pool_integration': [],
            'heartbeat_synchronization': [],
            'error_handling_consistency': []
        }
        
    async def test_multi_exchange_instantiation(self):
        """测试多交易所实例化协同"""
        print("🔍 测试多交易所实例化协同...")
        
        try:
            from websocket.okx_ws import OKXWebSocketClient
            from websocket.gate_ws import GateWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient
            
            # 同时创建三个交易所客户端
            clients = {}
            exchanges = ['okx', 'gate', 'bybit']
            client_classes = [OKXWebSocketClient, GateWebSocketClient, BybitWebSocketClient]
            
            for exchange, ClientClass in zip(exchanges, client_classes):
                try:
                    client = ClientClass("spot")
                    clients[exchange] = client
                    self.test_results['multi_exchange_instantiation'].append(
                        f"✅ {exchange.upper()}客户端实例化成功"
                    )
                except Exception as e:
                    self.test_results['multi_exchange_instantiation'].append(
                        f"❌ {exchange.upper()}客户端实例化失败: {str(e)}"
                    )
                    
            # 验证所有客户端都有必需的属性
            required_attrs = ['heartbeat_interval', 'timestamp_processor', 'exchange_name']
            
            for exchange, client in clients.items():
                missing_attrs = []
                for attr in required_attrs:
                    if not hasattr(client, attr):
                        missing_attrs.append(attr)
                        
                if not missing_attrs:
                    self.test_results['multi_exchange_instantiation'].append(
                        f"✅ {exchange.upper()}客户端属性完整"
                    )
                else:
                    self.test_results['multi_exchange_instantiation'].append(
                        f"❌ {exchange.upper()}客户端缺少属性: {missing_attrs}"
                    )
                    
        except Exception as e:
            self.test_results['multi_exchange_instantiation'].append(
                f"❌ 多交易所实例化测试异常: {str(e)}"
            )
            
    async def test_timestamp_sync_coordination(self):
        """测试时间戳同步协调性"""
        print("🔍 测试时间戳同步协调性...")
        
        try:
            from websocket.unified_timestamp_processor import (
                get_timestamp_processor, 
                initialize_all_timestamp_processors,
                check_all_timestamp_sync_health
            )
            
            # 测试统一时间戳处理器获取
            exchanges = ['okx', 'gate', 'bybit']
            processors = {}
            
            for exchange in exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    processors[exchange] = processor
                    
                    if processor is not None:
                        self.test_results['timestamp_sync_coordination'].append(
                            f"✅ {exchange.upper()}时间戳处理器获取成功"
                        )
                    else:
                        self.test_results['timestamp_sync_coordination'].append(
                            f"❌ {exchange.upper()}时间戳处理器获取失败"
                        )
                except Exception as e:
                    self.test_results['timestamp_sync_coordination'].append(
                        f"❌ {exchange.upper()}时间戳处理器获取异常: {str(e)}"
                    )
                    
            # 测试集中式初始化
            try:
                init_results = await initialize_all_timestamp_processors(force_sync=False)
                if isinstance(init_results, dict):
                    self.test_results['timestamp_sync_coordination'].append(
                        "✅ 集中式时间戳处理器初始化成功"
                    )
                else:
                    self.test_results['timestamp_sync_coordination'].append(
                        "❌ 集中式时间戳处理器初始化失败"
                    )
            except Exception as e:
                self.test_results['timestamp_sync_coordination'].append(
                    f"❌ 集中式初始化异常: {str(e)}"
                )
                
            # 测试健康状态检查
            try:
                health_status = await check_all_timestamp_sync_health()
                if isinstance(health_status, dict) and len(health_status) >= 3:
                    self.test_results['timestamp_sync_coordination'].append(
                        "✅ 时间戳同步健康状态检查成功"
                    )
                else:
                    self.test_results['timestamp_sync_coordination'].append(
                        "❌ 时间戳同步健康状态检查失败"
                    )
            except Exception as e:
                self.test_results['timestamp_sync_coordination'].append(
                    f"❌ 健康状态检查异常: {str(e)}"
                )
                
        except Exception as e:
            self.test_results['timestamp_sync_coordination'].append(
                f"❌ 时间戳同步协调测试异常: {str(e)}"
            )
            
    async def test_connection_pool_integration(self):
        """测试连接池集成"""
        print("🔍 测试连接池集成...")
        
        try:
            from websocket.unified_connection_pool_manager import get_connection_pool_manager
            
            # 获取连接池管理器
            pool_manager = get_connection_pool_manager()
            
            if pool_manager is not None:
                self.test_results['connection_pool_integration'].append(
                    "✅ 统一连接池管理器获取成功"
                )
                
                # 测试连接池管理器的基本方法
                required_methods = ['create_connection', 'handle_connection_issue', 'get_connection_status']
                missing_methods = []
                
                for method in required_methods:
                    if not hasattr(pool_manager, method):
                        missing_methods.append(method)
                        
                if not missing_methods:
                    self.test_results['connection_pool_integration'].append(
                        "✅ 连接池管理器接口完整"
                    )
                else:
                    self.test_results['connection_pool_integration'].append(
                        f"❌ 连接池管理器缺少方法: {missing_methods}"
                    )
                    
            else:
                self.test_results['connection_pool_integration'].append(
                    "❌ 统一连接池管理器获取失败"
                )
                
        except Exception as e:
            self.test_results['connection_pool_integration'].append(
                f"❌ 连接池集成测试异常: {str(e)}"
            )
            
    async def test_heartbeat_synchronization(self):
        """测试心跳同步"""
        print("🔍 测试心跳同步...")
        
        try:
            from websocket.okx_ws import OKXWebSocketClient
            from websocket.gate_ws import GateWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient
            
            clients = [
                ('OKX', OKXWebSocketClient("spot")),
                ('Gate.io', GateWebSocketClient("spot")),
                ('Bybit', BybitWebSocketClient("spot"))
            ]
            
            # 验证心跳间隔统一
            heartbeat_intervals = []
            for name, client in clients:
                heartbeat_intervals.append((name, client.heartbeat_interval))
                
            # 检查是否都是20秒
            if all(interval == 20 for _, interval in heartbeat_intervals):
                self.test_results['heartbeat_synchronization'].append(
                    "✅ 所有交易所心跳间隔统一为20秒"
                )
            else:
                self.test_results['heartbeat_synchronization'].append(
                    f"❌ 心跳间隔不统一: {heartbeat_intervals}"
                )
                
            # 验证心跳方法存在
            for name, client in clients:
                if hasattr(client, 'send_heartbeat'):
                    self.test_results['heartbeat_synchronization'].append(
                        f"✅ {name}心跳方法存在"
                    )
                else:
                    self.test_results['heartbeat_synchronization'].append(
                        f"❌ {name}缺少心跳方法"
                    )
                    
        except Exception as e:
            self.test_results['heartbeat_synchronization'].append(
                f"❌ 心跳同步测试异常: {str(e)}"
            )
            
    async def test_error_handling_consistency(self):
        """测试错误处理一致性"""
        print("🔍 测试错误处理一致性...")
        
        try:
            from websocket.okx_ws import OKXWebSocketClient
            from websocket.gate_ws import GateWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient
            
            clients = [
                ('OKX', OKXWebSocketClient("spot")),
                ('Gate.io', GateWebSocketClient("spot")),
                ('Bybit', BybitWebSocketClient("spot"))
            ]
            
            # 验证错误处理方法存在
            error_methods = ['_log_error', '_log_warning', '_log_info']
            
            for name, client in clients:
                missing_methods = []
                for method in error_methods:
                    if not hasattr(client, method):
                        missing_methods.append(method)
                        
                if not missing_methods:
                    self.test_results['error_handling_consistency'].append(
                        f"✅ {name}错误处理方法完整"
                    )
                else:
                    self.test_results['error_handling_consistency'].append(
                        f"❌ {name}缺少错误处理方法: {missing_methods}"
                    )
                    
            # 验证异常处理机制
            for name, client in clients:
                try:
                    # 测试设置无效交易对（应该有错误处理）
                    client.set_symbols([])  # 空列表应该被处理
                    self.test_results['error_handling_consistency'].append(
                        f"✅ {name}异常处理机制正常"
                    )
                except Exception as e:
                    # 如果抛出异常，说明没有适当的错误处理
                    self.test_results['error_handling_consistency'].append(
                        f"❌ {name}异常处理不当: {str(e)}"
                    )
                    
        except Exception as e:
            self.test_results['error_handling_consistency'].append(
                f"❌ 错误处理一致性测试异常: {str(e)}"
            )
            
    async def run_all_tests(self):
        """运行所有系统级联测试"""
        print("🚀 开始系统级联测试...")
        
        await self.test_multi_exchange_instantiation()
        await self.test_timestamp_sync_coordination()
        await self.test_connection_pool_integration()
        await self.test_heartbeat_synchronization()
        await self.test_error_handling_consistency()
        
        return self.test_results
        
    def generate_report(self):
        """生成测试报告"""
        report = []
        report.append("=" * 80)
        report.append("🔥 系统级联测试报告")
        report.append("=" * 80)
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for category, tests in self.test_results.items():
            if tests:
                report.append(f"\n📊 {category.replace('_', ' ').title()}:")
                for test in tests:
                    total_tests += 1
                    if test.startswith("✅"):
                        passed_tests += 1
                        icon = "✅"
                    else:
                        failed_tests += 1
                        icon = "❌"
                        
                    report.append(f"  {test}")
                    
        # 统计
        report.append(f"\n📈 测试统计:")
        report.append(f"  总测试数: {total_tests}")
        report.append(f"  通过: {passed_tests}")
        report.append(f"  失败: {failed_tests}")
        report.append(f"  成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "  成功率: 0%")
        
        if failed_tests == 0:
            report.append(f"\n🎉 所有系统级联测试通过！系统协同一致性100%！")
        else:
            report.append(f"\n⚠️ 发现 {failed_tests} 个系统级问题需要修复")
            
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)

async def main():
    """主函数"""
    tester = SystemIntegrationTest()
    
    try:
        results = await tester.run_all_tests()
        report = tester.generate_report()
        
        print(report)
        
        # 保存测试结果
        with open('system_integration_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        print("✅ 系统级联测试完成，结果已保存到 system_integration_test_results.json")
        
        # 计算退出码
        total_issues = sum(
            len([t for t in tests if t.startswith("❌")])
            for tests in results.values()
        )
        return 0 if total_issues == 0 else 1
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
