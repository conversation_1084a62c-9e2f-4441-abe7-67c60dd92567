#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket数据流阻塞精确诊断脚本
基于官方API规范和系统架构分析
"""

import asyncio
import time
import json
import logging
from typing import Dict, List, Any
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("WebSocketDiagnostic")

class WebSocketBlockingDiagnostic:
    """WebSocket数据流阻塞诊断器"""
    
    def __init__(self):
        self.results = {
            'okx_issues': [],
            'gate_issues': [],
            'bybit_status': [],
            'architecture_problems': [],
            'official_compliance': []
        }
        
    async def diagnose_okx_concurrent_issue(self):
        """诊断OKX WebSocket并发冲突问题"""
        logger.info("🔍 开始诊断OKX WebSocket并发冲突...")
        
        try:
            # 检查OKX实现中的并发问题
            from websocket.okx_ws import OKXWebSocketClient
            
            # 模拟创建OKX客户端
            client = OKXWebSocketClient("spot")
            
            # 检查是否存在_monitor_data_flow方法
            has_monitor_method = hasattr(client, '_monitor_data_flow')
            has_handle_blocking = hasattr(client, '_handle_data_flow_blocking')
            
            if has_monitor_method:
                self.results['okx_issues'].append({
                    'issue': 'WebSocket并发冲突',
                    'description': '存在_monitor_data_flow()监控任务与主消息循环冲突',
                    'severity': 'CRITICAL',
                    'cause': '多个协程同时调用ws.recv()导致RuntimeError',
                    'official_compliance': 'VIOLATION - OKX官方API不要求额外监控任务'
                })
                
            if has_handle_blocking:
                self.results['okx_issues'].append({
                    'issue': '数据流阻塞处理',
                    'description': '存在_handle_data_flow_blocking()方法',
                    'severity': 'HIGH',
                    'cause': '架构过度设计，违反官方API规范'
                })
                
            # 检查run方法中的监控任务启动
            import inspect
            run_source = inspect.getsource(client.run)
            if 'monitor_task' in run_source:
                self.results['okx_issues'].append({
                    'issue': '监控任务启动',
                    'description': 'run()方法中启动了额外的监控任务',
                    'severity': 'CRITICAL',
                    'line_reference': 'okx_ws.py:153 - asyncio.create_task(self._monitor_data_flow())'
                })
                
        except Exception as e:
            logger.error(f"OKX诊断失败: {e}")
            
    async def diagnose_gate_config_issue(self):
        """诊断Gate.io配置不一致问题"""
        logger.info("🔍 开始诊断Gate.io配置不一致...")
        
        try:
            from websocket.gate_ws import GateWebSocketClient
            from config.network_config import NetworkConfigManager
            
            # 检查Gate.io客户端配置
            client = GateWebSocketClient("spot")
            gate_heartbeat = client.heartbeat_interval
            
            # 检查全局配置
            config_manager = NetworkConfigManager()
            global_heartbeat = config_manager.get_websocket_config()['heartbeat_interval']
            
            if gate_heartbeat != global_heartbeat:
                self.results['gate_issues'].append({
                    'issue': '心跳间隔配置不一致',
                    'description': f'Gate.io局部配置{gate_heartbeat}秒 vs 全局配置{global_heartbeat}秒',
                    'severity': 'HIGH',
                    'file_location': 'websocket/gate_ws.py:62',
                    'official_compliance': 'PARTIAL - Gate.io官方支持灵活配置，但应保持一致性'
                })
                
            # 检查消息处理限流
            import inspect
            handle_message_source = inspect.getsource(client.handle_message)
            if '< 0.1' in handle_message_source and 'return' in handle_message_source:
                self.results['gate_issues'].append({
                    'issue': '消息处理过度限流',
                    'description': '100ms限流可能导致重要订单簿更新丢失',
                    'severity': 'MEDIUM',
                    'file_location': 'websocket/gate_ws.py:218',
                    'official_compliance': 'RISK - 可能违反数据完整性要求'
                })
                
        except Exception as e:
            logger.error(f"Gate.io诊断失败: {e}")
            
    async def diagnose_bybit_compliance(self):
        """诊断Bybit官方规范符合性"""
        logger.info("🔍 开始诊断Bybit官方规范符合性...")
        
        try:
            from websocket.bybit_ws import BybitWebSocketClient
            
            client = BybitWebSocketClient("spot")
            
            # 检查心跳间隔是否符合官方推荐的20秒
            heartbeat = client.heartbeat_interval
            if heartbeat == 20:
                self.results['bybit_status'].append({
                    'status': 'COMPLIANT',
                    'description': f'心跳间隔{heartbeat}秒符合官方推荐',
                    'official_requirement': 'Bybit官方推荐20秒心跳间隔'
                })
            else:
                self.results['bybit_status'].append({
                    'status': 'NON_COMPLIANT',
                    'description': f'心跳间隔{heartbeat}秒不符合官方推荐',
                    'official_requirement': 'Bybit官方推荐20秒心跳间隔'
                })
                
            # 检查是否有额外监控任务
            has_monitor = hasattr(client, '_monitor_data_flow')
            if not has_monitor:
                self.results['bybit_status'].append({
                    'status': 'COMPLIANT',
                    'description': '无额外监控任务，架构简洁正确',
                    'official_requirement': 'Bybit官方不要求额外监控机制'
                })
                
        except Exception as e:
            logger.error(f"Bybit诊断失败: {e}")
            
    async def check_architecture_problems(self):
        """检查架构问题"""
        logger.info("🔍 开始检查架构问题...")
        
        # 检查是否存在重复的监控机制
        monitoring_modules = []
        
        try:
            # 检查各种可能的监控实现
            import os
            websocket_dir = "websocket"
            
            for file in os.listdir(websocket_dir):
                if file.endswith('.py'):
                    file_path = os.path.join(websocket_dir, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if '_monitor_data_flow' in content:
                                monitoring_modules.append(file)
                    except:
                        continue
                        
            if len(monitoring_modules) > 0:
                self.results['architecture_problems'].append({
                    'problem': '重复监控机制',
                    'description': f'发现{len(monitoring_modules)}个文件包含监控逻辑',
                    'files': monitoring_modules,
                    'recommendation': '应统一使用基类的连接管理机制'
                })
                
        except Exception as e:
            logger.error(f"架构检查失败: {e}")
            
    async def verify_official_compliance(self):
        """验证官方API规范符合性"""
        logger.info("🔍 开始验证官方API规范符合性...")
        
        # 基于08文档的官方要求检查
        official_requirements = {
            'bybit': {
                'heartbeat_interval': 20,
                'heartbeat_format': '{"op": "ping"}',
                'subscription_method': 'batch_supported',
                'extra_monitoring': False
            },
            'gate': {
                'heartbeat_interval': 'flexible',  # 官方支持灵活配置
                'heartbeat_format': '{"channel": "spot.ping"}',
                'subscription_method': 'individual_required',
                'extra_monitoring': False
            },
            'okx': {
                'heartbeat_interval': 20,
                'heartbeat_format': '"ping"',
                'subscription_method': 'batch_supported', 
                'extra_monitoring': False
            }
        }
        
        for exchange, requirements in official_requirements.items():
            compliance_status = {
                'exchange': exchange,
                'compliant_items': [],
                'violations': []
            }
            
            # 这里可以添加更详细的符合性检查
            if not requirements['extra_monitoring']:
                compliance_status['compliant_items'].append(
                    '官方不要求额外监控机制'
                )
                
            self.results['official_compliance'].append(compliance_status)
            
    async def run_full_diagnosis(self):
        """运行完整诊断"""
        logger.info("🚀 开始WebSocket数据流阻塞完整诊断...")
        
        await self.diagnose_okx_concurrent_issue()
        await self.diagnose_gate_config_issue()
        await self.diagnose_bybit_compliance()
        await self.check_architecture_problems()
        await self.verify_official_compliance()
        
        return self.results
        
    def generate_report(self):
        """生成诊断报告"""
        report = []
        report.append("=" * 80)
        report.append("🔥 WebSocket数据流阻塞诊断报告")
        report.append("=" * 80)
        
        # OKX问题报告
        if self.results['okx_issues']:
            report.append("\n🚨 OKX关键问题:")
            for issue in self.results['okx_issues']:
                report.append(f"  ❌ {issue['issue']}: {issue['description']}")
                report.append(f"     严重程度: {issue['severity']}")
                if 'line_reference' in issue:
                    report.append(f"     位置: {issue['line_reference']}")
                    
        # Gate.io问题报告
        if self.results['gate_issues']:
            report.append("\n⚠️ Gate.io配置问题:")
            for issue in self.results['gate_issues']:
                report.append(f"  ⚠️ {issue['issue']}: {issue['description']}")
                report.append(f"     严重程度: {issue['severity']}")
                report.append(f"     位置: {issue['file_location']}")
                
        # Bybit状态报告
        if self.results['bybit_status']:
            report.append("\n✅ Bybit符合性状态:")
            for status in self.results['bybit_status']:
                icon = "✅" if status['status'] == 'COMPLIANT' else "❌"
                report.append(f"  {icon} {status['description']}")
                
        # 架构问题报告
        if self.results['architecture_problems']:
            report.append("\n🏗️ 架构问题:")
            for problem in self.results['architecture_problems']:
                report.append(f"  🔧 {problem['problem']}: {problem['description']}")
                if 'files' in problem:
                    report.append(f"     涉及文件: {', '.join(problem['files'])}")
                    
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)

async def main():
    """主函数"""
    diagnostic = WebSocketBlockingDiagnostic()
    
    try:
        results = await diagnostic.run_full_diagnosis()
        report = diagnostic.generate_report()
        
        print(report)
        
        # 保存诊断结果
        with open('websocket_diagnostic_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        logger.info("✅ 诊断完成，结果已保存到 websocket_diagnostic_results.json")
        
    except Exception as e:
        logger.error(f"诊断过程中发生错误: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
