#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础核心测试：模块单元功能验证
确保修复点本身100%稳定
"""

import asyncio
import time
import logging
import sys
import os
import traceback
import inspect
from typing import Dict, Any, List

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("BasicCoreTest")

class BasicCoreFunctionalityTest:
    """基础核心功能测试器"""
    
    def __init__(self):
        self.test_results = {
            'okx_core_tests': [],
            'gate_core_tests': [],
            'bybit_core_tests': [],
            'unified_module_tests': [],
            'interface_consistency_tests': []
        }
        
    async def test_okx_core_functionality(self):
        """测试OKX核心功能"""
        logger.info("🔍 测试OKX核心功能...")
        
        try:
            from websocket.okx_ws import OKXWebSocketClient
            
            # 测试1: 实例化
            client = OKXWebSocketClient("spot")
            self.test_results['okx_core_tests'].append({
                'test': 'OKX实例化',
                'status': 'PASS',
                'details': '成功创建OKX WebSocket客户端'
            })
            
            # 测试2: 确认监控任务已移除
            has_monitor = hasattr(client, '_monitor_data_flow')
            has_blocking_handler = hasattr(client, '_handle_data_flow_blocking')
            
            if not has_monitor and not has_blocking_handler:
                self.test_results['okx_core_tests'].append({
                    'test': '并发冲突源移除',
                    'status': 'PASS',
                    'details': '监控任务方法已完全移除'
                })
            else:
                self.test_results['okx_core_tests'].append({
                    'test': '并发冲突源移除',
                    'status': 'FAIL',
                    'details': f'仍存在监控方法: monitor={has_monitor}, handler={has_blocking_handler}'
                })
                
            # 测试3: run方法简洁性
            run_source = inspect.getsource(client.run)
            if 'monitor_task' not in run_source and 'asyncio.create_task' not in run_source:
                self.test_results['okx_core_tests'].append({
                    'test': 'run方法简洁性',
                    'status': 'PASS',
                    'details': 'run方法已简化，无额外任务'
                })
            else:
                self.test_results['okx_core_tests'].append({
                    'test': 'run方法简洁性',
                    'status': 'FAIL',
                    'details': 'run方法仍包含额外任务创建'
                })
                
            # 测试4: 心跳间隔
            if client.heartbeat_interval == 20:
                self.test_results['okx_core_tests'].append({
                    'test': '心跳间隔配置',
                    'status': 'PASS',
                    'details': f'心跳间隔正确: {client.heartbeat_interval}秒'
                })
            else:
                self.test_results['okx_core_tests'].append({
                    'test': '心跳间隔配置',
                    'status': 'FAIL',
                    'details': f'心跳间隔错误: {client.heartbeat_interval}秒，应为20秒'
                })
                
            # 测试5: 统一时间戳处理器
            if hasattr(client, 'timestamp_processor') and client.timestamp_processor is not None:
                self.test_results['okx_core_tests'].append({
                    'test': '统一时间戳处理器',
                    'status': 'PASS',
                    'details': '正确使用统一时间戳处理器'
                })
            else:
                self.test_results['okx_core_tests'].append({
                    'test': '统一时间戳处理器',
                    'status': 'FAIL',
                    'details': '缺少统一时间戳处理器'
                })
                
        except Exception as e:
            self.test_results['okx_core_tests'].append({
                'test': 'OKX核心功能',
                'status': 'ERROR',
                'details': f'测试异常: {str(e)}'
            })
            
    async def test_gate_core_functionality(self):
        """测试Gate.io核心功能"""
        logger.info("🔍 测试Gate.io核心功能...")
        
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            # 测试1: 实例化
            client = GateWebSocketClient("spot")
            self.test_results['gate_core_tests'].append({
                'test': 'Gate.io实例化',
                'status': 'PASS',
                'details': '成功创建Gate.io WebSocket客户端'
            })
            
            # 测试2: 心跳间隔统一
            if client.heartbeat_interval == 20:
                self.test_results['gate_core_tests'].append({
                    'test': '心跳间隔统一',
                    'status': 'PASS',
                    'details': f'心跳间隔已统一: {client.heartbeat_interval}秒'
                })
            else:
                self.test_results['gate_core_tests'].append({
                    'test': '心跳间隔统一',
                    'status': 'FAIL',
                    'details': f'心跳间隔未统一: {client.heartbeat_interval}秒，应为20秒'
                })
                
            # 测试3: 消息处理限流移除
            handle_message_source = inspect.getsource(client.handle_message)
            if '< 0.1' not in handle_message_source or 'return' not in handle_message_source.split('< 0.1')[0] if '< 0.1' in handle_message_source else True:
                self.test_results['gate_core_tests'].append({
                    'test': '消息处理限流移除',
                    'status': 'PASS',
                    'details': '消息处理限流已移除'
                })
            else:
                self.test_results['gate_core_tests'].append({
                    'test': '消息处理限流移除',
                    'status': 'FAIL',
                    'details': '仍存在消息处理限流逻辑'
                })
                
            # 测试4: 统一时间戳处理器
            if hasattr(client, 'timestamp_processor') and client.timestamp_processor is not None:
                self.test_results['gate_core_tests'].append({
                    'test': '统一时间戳处理器',
                    'status': 'PASS',
                    'details': '正确使用统一时间戳处理器'
                })
            else:
                self.test_results['gate_core_tests'].append({
                    'test': '统一时间戳处理器',
                    'status': 'FAIL',
                    'details': '缺少统一时间戳处理器'
                })
                
        except Exception as e:
            self.test_results['gate_core_tests'].append({
                'test': 'Gate.io核心功能',
                'status': 'ERROR',
                'details': f'测试异常: {str(e)}'
            })
            
    async def test_bybit_core_functionality(self):
        """测试Bybit核心功能"""
        logger.info("🔍 测试Bybit核心功能...")
        
        try:
            from websocket.bybit_ws import BybitWebSocketClient
            
            # 测试1: 实例化
            client = BybitWebSocketClient("spot")
            self.test_results['bybit_core_tests'].append({
                'test': 'Bybit实例化',
                'status': 'PASS',
                'details': '成功创建Bybit WebSocket客户端'
            })
            
            # 测试2: 心跳间隔符合官方推荐
            if client.heartbeat_interval == 20:
                self.test_results['bybit_core_tests'].append({
                    'test': '心跳间隔官方推荐',
                    'status': 'PASS',
                    'details': f'心跳间隔符合官方推荐: {client.heartbeat_interval}秒'
                })
            else:
                self.test_results['bybit_core_tests'].append({
                    'test': '心跳间隔官方推荐',
                    'status': 'FAIL',
                    'details': f'心跳间隔不符合官方推荐: {client.heartbeat_interval}秒，应为20秒'
                })
                
            # 测试3: 架构简洁性
            has_monitor = hasattr(client, '_monitor_data_flow')
            if not has_monitor:
                self.test_results['bybit_core_tests'].append({
                    'test': '架构简洁性',
                    'status': 'PASS',
                    'details': '架构简洁，无额外监控任务'
                })
            else:
                self.test_results['bybit_core_tests'].append({
                    'test': '架构简洁性',
                    'status': 'FAIL',
                    'details': '存在额外监控任务'
                })
                
        except Exception as e:
            self.test_results['bybit_core_tests'].append({
                'test': 'Bybit核心功能',
                'status': 'ERROR',
                'details': f'测试异常: {str(e)}'
            })
            
    async def test_unified_modules(self):
        """测试统一模块使用"""
        logger.info("🔍 测试统一模块使用...")
        
        try:
            # 测试统一时间戳处理器
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            for exchange in ['okx', 'gate', 'bybit']:
                processor = get_timestamp_processor(exchange)
                if processor is not None:
                    self.test_results['unified_module_tests'].append({
                        'test': f'{exchange.upper()}统一时间戳处理器',
                        'status': 'PASS',
                        'details': f'{exchange}正确使用统一时间戳处理器'
                    })
                else:
                    self.test_results['unified_module_tests'].append({
                        'test': f'{exchange.upper()}统一时间戳处理器',
                        'status': 'FAIL',
                        'details': f'{exchange}统一时间戳处理器获取失败'
                    })
                    
            # 测试统一连接池管理器
            from websocket.unified_connection_pool_manager import get_connection_pool_manager
            pool_manager = get_connection_pool_manager()
            if pool_manager is not None:
                self.test_results['unified_module_tests'].append({
                    'test': '统一连接池管理器',
                    'status': 'PASS',
                    'details': '统一连接池管理器可用'
                })
            else:
                self.test_results['unified_module_tests'].append({
                    'test': '统一连接池管理器',
                    'status': 'FAIL',
                    'details': '统一连接池管理器获取失败'
                })
                
        except Exception as e:
            self.test_results['unified_module_tests'].append({
                'test': '统一模块',
                'status': 'ERROR',
                'details': f'测试异常: {str(e)}'
            })
            
    async def test_interface_consistency(self):
        """测试接口一致性"""
        logger.info("🔍 测试接口一致性...")
        
        try:
            from websocket.okx_ws import OKXWebSocketClient
            from websocket.gate_ws import GateWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient
            
            clients = [
                ('OKX', OKXWebSocketClient("spot")),
                ('Gate.io', GateWebSocketClient("spot")),
                ('Bybit', BybitWebSocketClient("spot"))
            ]
            
            # 测试接口方法一致性
            required_methods = ['run', 'subscribe_channels', 'handle_message', 'send_heartbeat']
            
            for name, client in clients:
                missing_methods = []
                for method in required_methods:
                    if not hasattr(client, method):
                        missing_methods.append(method)
                        
                if not missing_methods:
                    self.test_results['interface_consistency_tests'].append({
                        'test': f'{name}接口完整性',
                        'status': 'PASS',
                        'details': f'{name}所有必需接口方法存在'
                    })
                else:
                    self.test_results['interface_consistency_tests'].append({
                        'test': f'{name}接口完整性',
                        'status': 'FAIL',
                        'details': f'{name}缺少方法: {missing_methods}'
                    })
                    
            # 测试心跳间隔一致性
            heartbeat_intervals = [(name, client.heartbeat_interval) for name, client in clients]
            if all(interval == 20 for _, interval in heartbeat_intervals):
                self.test_results['interface_consistency_tests'].append({
                    'test': '心跳间隔一致性',
                    'status': 'PASS',
                    'details': '所有交易所心跳间隔统一为20秒'
                })
            else:
                self.test_results['interface_consistency_tests'].append({
                    'test': '心跳间隔一致性',
                    'status': 'FAIL',
                    'details': f'心跳间隔不一致: {heartbeat_intervals}'
                })
                
        except Exception as e:
            self.test_results['interface_consistency_tests'].append({
                'test': '接口一致性',
                'status': 'ERROR',
                'details': f'测试异常: {str(e)}'
            })
            
    async def run_all_tests(self):
        """运行所有基础核心测试"""
        logger.info("🚀 开始基础核心功能测试...")
        
        await self.test_okx_core_functionality()
        await self.test_gate_core_functionality()
        await self.test_bybit_core_functionality()
        await self.test_unified_modules()
        await self.test_interface_consistency()
        
        return self.test_results
        
    def generate_report(self):
        """生成测试报告"""
        report = []
        report.append("=" * 80)
        report.append("🔥 基础核心功能测试报告")
        report.append("=" * 80)
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        error_tests = 0
        
        for category, tests in self.test_results.items():
            if tests:
                report.append(f"\n📊 {category.replace('_', ' ').title()}:")
                for test in tests:
                    total_tests += 1
                    status = test['status']
                    if status == 'PASS':
                        passed_tests += 1
                        icon = "✅"
                    elif status == 'FAIL':
                        failed_tests += 1
                        icon = "❌"
                    else:
                        error_tests += 1
                        icon = "⚠️"
                        
                    report.append(f"  {icon} {test['test']}: {test['details']}")
                    
        # 统计
        report.append(f"\n📈 测试统计:")
        report.append(f"  总测试数: {total_tests}")
        report.append(f"  通过: {passed_tests}")
        report.append(f"  失败: {failed_tests}")
        report.append(f"  错误: {error_tests}")
        report.append(f"  成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "  成功率: 0%")
        
        if failed_tests == 0 and error_tests == 0:
            report.append(f"\n🎉 所有基础核心测试通过！修复点100%稳定！")
        else:
            report.append(f"\n⚠️ 发现 {failed_tests + error_tests} 个问题需要修复")
            
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)

async def main():
    """主函数"""
    tester = BasicCoreFunctionalityTest()
    
    try:
        results = await tester.run_all_tests()
        report = tester.generate_report()
        
        print(report)
        
        # 保存测试结果
        import json
        with open('basic_core_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        logger.info("✅ 基础核心测试完成，结果已保存到 basic_core_test_results.json")
        
        # 计算退出码
        total_issues = sum(
            len([t for t in tests if t['status'] in ['FAIL', 'ERROR']])
            for tests in results.values()
        )
        return 0 if total_issues == 0 else 1
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
