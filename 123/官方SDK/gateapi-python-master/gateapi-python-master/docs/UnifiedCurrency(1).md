# UnifiedCurrency

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** | Currency name | [optional] 
**prec** | **str** | Currency precision | [optional] 
**min_borrow_amount** | **str** | The minimum debit limit is the unit of currency | [optional] 
**user_max_borrow_amount** | **str** | The minimum debit limit is the unit of currency | [optional] 
**total_max_borrow_amount** | **str** | The maximum debit limit for the platform is USDT | [optional] 
**loan_status** | **str** | Does the lending status  - &#x60;disable&#x60; : Loans are prohibited  - &#x60;enable&#x60;: Support lending | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


