# UniLoanRecord

Loan records
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **str** | type: borrow - borrow, repay - repay | [optional] [readonly] 
**currency_pair** | **str** | Currency pair | [optional] [readonly] 
**currency** | **str** | Currency | [optional] [readonly] 
**amount** | **str** | The amount of lending or repaying | [optional] [readonly] 
**create_time** | **int** | Created time | [optional] [readonly] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


