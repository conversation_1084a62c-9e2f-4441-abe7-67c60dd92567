2025-08-03 09:28:42.290 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 09:28:43.751 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 09:29:54.149 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:54.162 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.162 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.162 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:54.167 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.167 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:54.172 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.172 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.172 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.172 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.173 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.173 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.173 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.173 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:54.203 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.205 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.205 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:55.303 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-03 09:29:55.303 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-03 09:29:55.303 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-03 09:30:07.950 [ERROR] [websocket] [GATE] ❌ [GATE-SPOT] 订阅失败: {'time': **********, 'time_ms': *************, 'conn_id': '441d61d60e3d1e33', 'trace_id': '774346b3838b4cf769e542f4bbd84975', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '774346b3838b4cf769e542f4bbd84975'}
2025-08-03 09:30:07.950 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': **********, 'time_ms': *************, 'conn_id': '441d61d60e3d1e33', 'trace_id': '774346b3838b4cf769e542f4bbd84975', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '774346b3838b4cf769e542f4bbd84975'}"}
2025-08-03 09:30:12.393 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 09:30:12.919 [ERROR] [websocket] [GATE] ❌ [GATE-FUTURES] 订阅失败: {'time': 1754206212, 'time_ms': 1754206212857, 'conn_id': '074ff4f7e8c8301b', 'trace_id': 'b916ee8f7f107bcaef1f2f374516db37', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}
2025-08-03 09:30:12.919 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'futures.order_book', 'market_type': 'futures', 'error_message': "{'time': 1754206212, 'time_ms': 1754206212857, 'conn_id': '074ff4f7e8c8301b', 'trace_id': 'b916ee8f7f107bcaef1f2f374516db37', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}"}
2025-08-03 09:30:13.871 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 09:30:22.914 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 09:30:24.401 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 09:30:36.071 [ERROR] [websocket] [GATE] ❌ [GATE-SPOT] 订阅失败: {'time': 1754206235, 'time_ms': 1754206235947, 'conn_id': '4c3299e6f2256a9d', 'trace_id': 'fe2af954fc1aa11b9fc808884afdb843', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': 'fe2af954fc1aa11b9fc808884afdb843'}
2025-08-03 09:30:36.072 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754206235, 'time_ms': 1754206235947, 'conn_id': '4c3299e6f2256a9d', 'trace_id': 'fe2af954fc1aa11b9fc808884afdb843', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': 'fe2af954fc1aa11b9fc808884afdb843'}"}
2025-08-03 09:30:36.073 [ERROR] [websocket] [GATE] ❌ [GATE-FUTURES] 订阅失败: {'time': 1754206235, 'time_ms': 1754206235946, 'conn_id': '84a9f40c5e65e65a', 'trace_id': '6c20cd2cb41bdb7e62f6d43307cbea83', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}
2025-08-03 09:30:36.074 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'futures.order_book', 'market_type': 'futures', 'error_message': "{'time': 1754206235, 'time_ms': 1754206235946, 'conn_id': '84a9f40c5e65e65a', 'trace_id': '6c20cd2cb41bdb7e62f6d43307cbea83', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}"}
2025-08-03 09:31:27.864 [ERROR] [asyncio] Future exception was never retrieved
future: <Future finished exception=ConnectionClosedError(None, Close(code=1000, reason=''), None)>
websockets.exceptions.ConnectionClosedError: sent 1000 (OK); no close frame received
2025-08-03 09:31:41.431 [ERROR] [websocket] [OKX] 连接错误: server rejected WebSocket connection: HTTP 503
Traceback (most recent call last):
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/ws_client.py", line 268, in _connect
    self.ws = await asyncio.wait_for(
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 655, in __await_impl_timeout__
    return await self.__await_impl__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 662, in __await_impl__
    await protocol.handshake(
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 329, in handshake
    raise InvalidStatusCode(status_code, response_headers)
websockets.exceptions.InvalidStatusCode: server rejected WebSocket connection: HTTP 503
2025-08-03 09:31:41.869 [ERROR] [websocket.error_handler] [OKX] connection_error: server rejected WebSocket connection: HTTP 503
2025-08-03 09:31:45.777 [ERROR] [asyncio] Task exception was never retrieved
future: <Task finished name='Task-34460' coro=<UnifiedErrorHandler.handle_error() done, defined at /root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/error_handler.py:183> exception=TypeError("'int' object is not iterable")>
Traceback (most recent call last):
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/error_handler.py", line 206, in handle_error
    return await self._execute_recovery_strategy(error_event, strategy, context or {})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/error_handler.py", line 230, in _execute_recovery_strategy
    self._update_recovery_stats(True)
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/error_handler.py", line 289, in _update_recovery_stats
    total_attempts = sum(len([e for e in self.error_events if e.retry_count > 0]))
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'int' object is not iterable
2025-08-03 09:31:46.812 [ERROR] [websocket] [OKX] 订阅频道请求发送失败: 批次1
2025-08-03 09:31:46.812 [ERROR] [websocket_subscription_failure] [okx] 订阅批次发送失败 | {'market_type': 'spot', 'batch_number': 1}
2025-08-03 09:31:48.215 [ERROR] [websocket] [OKX] 连接错误: 'NoneType' object has no attribute 'close'
Traceback (most recent call last):
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/ws_client.py", line 326, in _connect
    await self.ws.close()
          ^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'close'
2025-08-03 09:31:49.351 [ERROR] [websocket.error_handler] [OKX] unknown_error: 'NoneType' object has no attribute 'close'
2025-08-03 09:31:53.198 [ERROR] [websocket] [OKX] 订阅频道请求发送失败: 批次1
2025-08-03 09:31:53.198 [ERROR] [websocket_subscription_failure] [okx] 订阅批次发送失败 | {'market_type': 'spot', 'batch_number': 1}
2025-08-03 09:31:53.198 [ERROR] [websocket] [OKX] 连接错误: server rejected WebSocket connection: HTTP 503
Traceback (most recent call last):
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/ws_client.py", line 268, in _connect
    self.ws = await asyncio.wait_for(
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 655, in __await_impl_timeout__
    return await self.__await_impl__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 662, in __await_impl__
    await protocol.handshake(
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 329, in handshake
    raise InvalidStatusCode(status_code, response_headers)
websockets.exceptions.InvalidStatusCode: server rejected WebSocket connection: HTTP 503
2025-08-03 09:31:53.199 [ERROR] [websocket] [OKX] 连接错误: server rejected WebSocket connection: HTTP 503
Traceback (most recent call last):
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/ws_client.py", line 268, in _connect
    self.ws = await asyncio.wait_for(
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 655, in __await_impl_timeout__
    return await self.__await_impl__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 662, in __await_impl__
    await protocol.handshake(
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 329, in handshake
    raise InvalidStatusCode(status_code, response_headers)
websockets.exceptions.InvalidStatusCode: server rejected WebSocket connection: HTTP 503
2025-08-03 09:31:54.449 [ERROR] [websocket] [OKX] 订阅频道请求发送失败: 批次1
2025-08-03 09:31:54.449 [ERROR] [websocket_subscription_failure] [okx] 订阅批次发送失败 | {'market_type': 'spot', 'batch_number': 1}
2025-08-03 09:31:54.450 [ERROR] [websocket.error_handler] [OKX] connection_error: server rejected WebSocket connection: HTTP 503
2025-08-03 09:31:54.450 [ERROR] [websocket.error_handler] [OKX] connection_error: server rejected WebSocket connection: HTTP 503
2025-08-03 09:31:54.451 [ERROR] [websocket] [OKX] 连接错误: server rejected WebSocket connection: HTTP 503
Traceback (most recent call last):
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/ws_client.py", line 268, in _connect
    self.ws = await asyncio.wait_for(
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 655, in __await_impl_timeout__
    return await self.__await_impl__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 662, in __await_impl__
    await protocol.handshake(
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 329, in handshake
    raise InvalidStatusCode(status_code, response_headers)
websockets.exceptions.InvalidStatusCode: server rejected WebSocket connection: HTTP 503
2025-08-03 09:31:54.452 [ERROR] [websocket] [OKX] 连接错误: server rejected WebSocket connection: HTTP 503
Traceback (most recent call last):
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/ws_client.py", line 268, in _connect
    self.ws = await asyncio.wait_for(
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 655, in __await_impl_timeout__
    return await self.__await_impl__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 662, in __await_impl__
    await protocol.handshake(
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 329, in handshake
    raise InvalidStatusCode(status_code, response_headers)
websockets.exceptions.InvalidStatusCode: server rejected WebSocket connection: HTTP 503
2025-08-03 09:31:54.932 [ERROR] [websocket] [OKX] 订阅频道请求发送失败: 批次1
2025-08-03 09:31:54.932 [ERROR] [websocket_subscription_failure] [okx] 订阅批次发送失败 | {'market_type': 'spot', 'batch_number': 1}
2025-08-03 09:31:54.932 [ERROR] [websocket] [OKX] 连接错误: 'NoneType' object has no attribute 'close'
Traceback (most recent call last):
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/ws_client.py", line 326, in _connect
    await self.ws.close()
          ^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'close'
2025-08-03 09:32:00.139 [ERROR] [websocket.error_handler] [OKX] connection_error: server rejected WebSocket connection: HTTP 503
2025-08-03 09:32:00.140 [ERROR] [websocket.error_handler] [OKX] connection_error: server rejected WebSocket connection: HTTP 503
2025-08-03 09:32:02.297 [ERROR] [websocket] [OKX] 订阅频道请求发送失败: 批次1
2025-08-03 09:32:02.297 [ERROR] [websocket_subscription_failure] [okx] 订阅批次发送失败 | {'market_type': 'spot', 'batch_number': 1}
2025-08-03 09:32:02.298 [ERROR] [websocket] [OKX] 连接错误: 'NoneType' object has no attribute 'close'
Traceback (most recent call last):
  File "/root/myproject/123/68B 还在修复阻塞问题，但是修复连接池/123/websocket/ws_client.py", line 326, in _connect
    await self.ws.close()
          ^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'close'
2025-08-03 09:32:02.299 [ERROR] [websocket.error_handler] [OKX] unknown_error: 'NoneType' object has no attribute 'close'
